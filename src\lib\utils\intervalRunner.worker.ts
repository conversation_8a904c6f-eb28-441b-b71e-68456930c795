export type RequestMessageType = "start" | "stop";
export type ResponseMessageType = "elapsed";

export interface BaseRequestMessage {
	type: RequestMessageType;
	intervalId: string;
}

export interface BaseResponseMessage {
	type: ResponseMessageType;
	intervalId: string;
}

export interface StartRequestMessage extends BaseRequestMessage {
	type: "start";
	intervalMs: number;
	immediateExecution: boolean;
}

export interface StopRequestMessage extends BaseRequestMessage {
	type: "stop";
}

export interface ElapsedResponseMessage extends BaseResponseMessage {
	type: "elapsed";
	timestamp: DOMHighResTimeStamp;
}

export type RequestMessage = StartRequestMessage | StopRequestMessage;
export type ResponseMessage = ElapsedResponseMessage;

const _intervalIdToInternalIntervalIdMap: Record<string, NodeJS.Timeout | undefined> = {};

onmessage = (messageEvent: MessageEvent<RequestMessage>) => {
	const message = messageEvent.data;
	switch (message.type) {
		case "start":
			_start(message.intervalId, message.intervalMs, message.immediateExecution);
			break;
		case "stop":
			_stop(message.intervalId);
			break;
		default:
			console.log("Received an unknown interval runner worker request message type");
	}
};

function _start(intervalId: string, intervalMs: number, immediateExecution: boolean) {
	const internalIntervalId = setInterval(() => _onIntervalElapsed(intervalId), intervalMs);
	if (_intervalIdToInternalIntervalIdMap[intervalId] !== undefined) {
		console.warn("Provided interval id might be already in use");
	}

	_intervalIdToInternalIntervalIdMap[intervalId] = internalIntervalId;

	if (immediateExecution) {
		_onIntervalElapsed(intervalId);
	}
}

function _stop(intervalId: string) {
	const internalIntervalId = _intervalIdToInternalIntervalIdMap[intervalId];
	if (internalIntervalId === undefined) {
		return;
	}

	clearInterval(internalIntervalId);
	_intervalIdToInternalIntervalIdMap[intervalId] = undefined;
}

function _onIntervalElapsed(intervalId: string) {
	const timestamp = performance.now();
	const elapsedMessage: ElapsedResponseMessage = {
		type: "elapsed",
		timestamp: timestamp,
		intervalId: intervalId
	};

	postMessage(elapsedMessage);
}

export {};
