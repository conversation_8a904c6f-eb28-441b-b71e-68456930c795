<script lang="ts">
	import type { VideoInfoProps } from "$lib/models/props/videoInfoProps";
	import InfoPoster from "$lib/components/ui/video-info/info-poster/InfoPoster.svelte";
	import InfoExternalLinks from "./info-external-links/InfoExternalLinks.svelte";
	import InfoHeadline from "$lib/components/ui/video-info/info-headline/InfoHeadline.svelte";
	import type { InfoColumnItem } from "$lib/models/props/infoColumnProps";
	import InfoColumn from "./info-column/InfoColumn.svelte";
	import {
		isMovieOrSeriesInfo,
		isMovieInfo,
		isSeriesInfo,
		type BaseMoviesAndSeriesCredits
	} from "$lib/models/sourceContainer";
	import type { ExternalInfoType } from "$lib/types/externalInfoType";
	import InfoCredits from "./info-credits/InfoCredits.svelte";
	import Separator from "$lib/components/ui/separator/separator.svelte";

	const USD_FORMATTER = new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD",
		trailingZeroDisplay: "stripIfInteger"
	});

	let { sourceContainer, isEpisodeTitleBlurred }: VideoInfoProps = $props();

	let posterImageUrl: string | undefined = $derived(
		isMovieOrSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.posterImageUrl
			: undefined
	);

	let imdbId: string | undefined = $derived(
		isMovieOrSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.imdbId
			: undefined
	);

	let contentType: ExternalInfoType | undefined = $derived(
		isMovieOrSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.type
			: undefined
	);

	let title: string | undefined = $derived(
		isMovieOrSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.title
			: undefined
	);

	let tagline: string | undefined = $derived(
		isMovieInfo(sourceContainer.externalInfo) ? sourceContainer.externalInfo.tagline : undefined
	);

	let episodeTitle: string | undefined = $derived(
		isSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.episodeTitle
			: undefined
	);

	let episodeNumber: number | undefined = $derived(
		isSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.episodeNumber
			: undefined
	);

	let seasonNumber: number | undefined = $derived(
		isSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.seasonNumber
			: undefined
	);

	let description: string | undefined = $derived(
		isMovieOrSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.description
			: undefined
	);

	let credits: BaseMoviesAndSeriesCredits | undefined = $derived(
		isMovieOrSeriesInfo(sourceContainer.externalInfo)
			? sourceContainer.externalInfo.credits
			: undefined
	);

	let columnItems: InfoColumnItem[] = $derived.by(() => {
		if (!sourceContainer.externalInfo) {
			return [];
		}

		if (!isMovieOrSeriesInfo(sourceContainer.externalInfo)) {
			return [];
		}

		let items: InfoColumnItem[] = [];
		if (sourceContainer.externalInfo.genres) {
			items.push({
				title: "Genres",
				contentItems: sourceContainer.externalInfo.genres.map((genre) => ({
					value: genre
				}))
			});
		}

		if (sourceContainer.externalInfo.spokenLanguages) {
			items.push({
				title: "Spoken Languages",
				contentItems: sourceContainer.externalInfo.spokenLanguages.map((spokenLanguage) => ({
					value: spokenLanguage
				}))
			});
		}

		if (sourceContainer.externalInfo.originCountries) {
			items.push({
				title: "Origin Countries",
				contentItems: sourceContainer.externalInfo.originCountries.map((originCountry) => ({
					value: originCountry
				}))
			});
		}

		if (sourceContainer.externalInfo.releaseDate) {
			const dateOptions: Intl.DateTimeFormatOptions = {
				year: "numeric",
				month: "long",
				day: "numeric"
			};
			const date = new Date(sourceContainer.externalInfo.releaseDate);
			items.push({
				title: "Release Date",
				contentItems: [{ value: date.toLocaleDateString("en-US", dateOptions) }]
			});
		}

		if (sourceContainer.externalInfo.networks) {
			items.push({
				title: "Networks",
				contentItems: sourceContainer.externalInfo.networks.map((network) => ({
					value: network.name ?? "",
					imageUrl: network.logoImageUrl
				}))
			});
		}

		if (sourceContainer.externalInfo.productionCompanies) {
			items.push({
				title: "Production",
				contentItems: sourceContainer.externalInfo.productionCompanies.map((company) => ({
					value: company.name ?? "",
					imageUrl: company.logoImageUrl
				}))
			});
		}

		if (
			isMovieInfo(sourceContainer.externalInfo) &&
			sourceContainer.externalInfo.budget &&
			sourceContainer.externalInfo.budget > 0
		) {
			items.push({
				title: "Budget",
				contentItems: [{ value: USD_FORMATTER.format(sourceContainer.externalInfo.budget) }]
			});
		}

		return items;
	});
</script>

{#if sourceContainer.externalInfo}
	<div class="flex gap-4 flex-col sm:flex-row">
		<div class="flex flex-col h-min w-full sm:w-min shadows-card-level0 bg-popover rounded-xl">
			<div class="flex justify-center mx-px mt-px poster-rounded-xl overflow-hidden">
				<InfoPoster {posterImageUrl}></InfoPoster>
			</div>
			<div class="flex flex-col px-4 pb-1.5">
				{#if imdbId && contentType}
					<Separator class="bg-background shadows-separator-level1 mx-[-1rem] w-auto"></Separator>
					<div class="mt-4">
						<InfoExternalLinks {imdbId} {contentType}></InfoExternalLinks>
					</div>
					<Separator class="bg-background shadows-separator-level1 mt-4 mx-[-1rem] w-auto"
					></Separator>
				{/if}
				<div class="mt-3">
					<InfoColumn items={columnItems} separatorClass="mx-[-1rem] w-auto"></InfoColumn>
				</div>
			</div>
		</div>
		<div class="flex flex-col w-full">
			<div class="rounded-xl px-4 pt-2 pb-3 bg-popover shadows-card-level0">
				<InfoHeadline
					{title}
					{tagline}
					{episodeTitle}
					{episodeNumber}
					{seasonNumber}
					{isEpisodeTitleBlurred}
				></InfoHeadline>
				{#if description}
					<span class="block mt-4">{description}</span>
				{/if}
			</div>

			<div class="rounded-xl px-4 pt-0 pb-4 bg-popover shadows-card-level0 mt-4">
				{#if credits}
					<InfoCredits {credits}></InfoCredits>
				{/if}
			</div>
		</div>
	</div>
{/if}

<style>
	.poster-rounded-xl {
		border-top-left-radius: calc(0.75rem - 1px);
		border-top-right-radius: calc(0.75rem - 1px);
	}
</style>
