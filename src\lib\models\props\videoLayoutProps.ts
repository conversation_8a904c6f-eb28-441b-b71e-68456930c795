export type VideoLayoutProps = {
	thumbnails?: string;
	isChapterTitleVisible?: boolean;
	isTheatreMode?: boolean;
	isMutedBannerVisible?: boolean;
	isSubtitleMenuVisible?: boolean;
	isSubtitleShiftMenuVisible?: boolean;
	errorMessage?: string;
	onToggleTheatreMode?: (isTheatreMode: boolean) => void;
	onClickMutedBannerUnmute?: () => void;
	onSetSubtitleOffsetByUser?: (src: string, offset: number) => void;
};
