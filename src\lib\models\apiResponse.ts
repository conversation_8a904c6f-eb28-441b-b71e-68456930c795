import type { ApiResponseParams, ErrorDto } from "./apiResponseParams";

export class ApiResponse<TBody> {
	private _statusCode: number;
	private _statusText: string;
	private _body: TBody | undefined;
	private _error: ErrorDto | undefined;

	constructor(params: ApiResponseParams<TBody>) {
		this._statusCode = params.statusCode;
		this._statusText = params.statusText;
		this._body = params.body;
		this._error = params.error;
	}

	public get statusCode(): number {
		return this._statusCode;
	}

	public get statusText(): string {
		return this._statusText;
	}

	public get body(): TBody | undefined {
		return this._body;
	}

	public get error(): ErrorDto | undefined {
		return this._error;
	}

	public get ok(): boolean {
		return this.statusCode >= 200 && this.statusCode <= 299;
	}
}
