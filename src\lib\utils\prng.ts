const MIN = -2147483648;
const MAX = 2147483647;

function xorShift(value: number) {
	value ^= value << 13;
	value ^= value >> 17;
	value ^= value << 5;

	return value;
}

function hashSeed(seed: string) {
	let hash = 0;

	for (let i = 0; i < seed.length; i++) {
		hash = ((hash << 5) - hash + seed.charCodeAt(i)) | 0;
		hash = xorShift(hash);
	}

	return hash;
}

export class Prng {
	private value: number;

	constructor(seed: string) {
		this.value = hashSeed(seed) || 1;
	}

	public next() {
		this.value = xorShift(this.value);

		return this.value;
	}

	public integer(min: number, max: number) {
		return Math.floor(((this.next() - MIN) / (MAX - MIN)) * (max + 1 - min) + min);
	}

	public pick<T>(array: T[], fallback?: T) {
		if (array.length === 0) {
			this.next();

			return fallback;
		}

		return array[this.integer(0, array.length - 1)] ?? fallback;
	}
}
