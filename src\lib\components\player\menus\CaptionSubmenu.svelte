<script lang="ts">
	import type { CaptionSubmenuProps } from "$lib/models/props/captionSubmenuProps";
	import CaptionShift from "./CaptionShift.svelte";
	import MenuRadio from "./MenuRadio.svelte";
	import Submenu from "./Submenu.svelte";

	let { isSubtitleShiftMenuVisible, onSetSubtitleOffsetByUser }: CaptionSubmenuProps = $props();
</script>

<Submenu label="Captions">
	{#snippet icon()}
		<media-icon class="h-5 w-5 shrink-0" type="closed-captions"></media-icon>
	{/snippet}

	{#snippet content()}
		<media-captions-radio-group class="w-full flex flex-col">
			<template>
				<MenuRadio></MenuRadio>
			</template>
		</media-captions-radio-group>
	{/snippet}

	{#snippet footer()}
		<div class="caption-shift-container hidden w-full {isSubtitleShiftMenuVisible ? 'mt-2' : ''}">
			{#if isSubtitleShiftMenuVisible}
				<CaptionShift {onSetSubtitleOffsetByUser}></CaptionShift>
			{/if}
		</div>
	{/snippet}
</Submenu>

<style>
	:global([data-open]) + .caption-shift-container {
		display: block;
	}
</style>
