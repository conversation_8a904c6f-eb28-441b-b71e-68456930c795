diff --git a/dist/preset.js b/dist/preset.js
index 7bda8e0080e19b7351e984069035aebe1f51a8fc..5c55891b9838d3b82b996c5b0902e52816a59a04 100644
--- a/dist/preset.js
+++ b/dist/preset.js
@@ -1,5 +1,5 @@
 "use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var preset_exports={};__export(preset_exports,{core:()=>core,viteFinal:()=>viteFinal});module.exports=__toCommonJS(preset_exports);var import_node_path3=require("path");var import_node_fs=require("fs"),import_node_path2=require("path"),import_node_logger=require("storybook/internal/node-logger"),import_magic_string=__toESM(require("magic-string")),import_svelte_preprocess=require("svelte-preprocess"),import_compiler2=require("svelte/compiler"),import_sveltedoc_parser=__toESM(require("sveltedoc-parser"));var import_node_path=__toESM(require("path")),import_svelte2tsx=__toESM(require("svelte2tsx")),import_compiler=require("svelte/compiler"),import_typescript=__toESM(require("typescript"));function convertType(type,checker){if(type.flags&import_typescript.default.TypeFlags.Any)return{type:"any"};if(type.flags&import_typescript.default.TypeFlags.Number)return{type:"number"};if(type.flags&import_typescript.default.TypeFlags.String)return{type:"string"};if(type.flags&import_typescript.default.TypeFlags.Boolean)return{type:"boolean"};if(type.flags&import_typescript.default.TypeFlags.ESSymbol)return{type:"symbol"};if(type.flags&import_typescript.default.TypeFlags.Null)return{type:"null"};if(type.flags&import_typescript.default.TypeFlags.Undefined)return{type:"undefined"};if(type.flags&import_typescript.default.TypeFlags.Void)return{type:"void"};if(type.getCallSignatures().length>0)return{type:"function",text:checker.typeToString(type)};if(type.flags&import_typescript.default.TypeFlags.Object)return checker.getIndexTypeOfType(type,import_typescript.default.IndexKind.Number)?{type:"array",text:checker.typeToString(type)}:{type:"object",text:checker.typeToString(type)};if(type.isNumberLiteral()||type.isStringLiteral())return{type:"literal",value:type.value,text:type.flags&import_typescript.default.TypeFlags.EnumLiteral?checker.typeToString(type):JSON.stringify(type.value)};if(type.flags&import_typescript.default.TypeFlags.BooleanLiteral){let text=checker.typeToString(type);return{type:"literal",value:text==="true",text}}if(type.isUnion()){let types=type.types.map(t=>convertType(t,checker)).filter(t=>t!==void 0&&t.type!=="undefined"),idxTrue=types.findIndex(t=>t.type==="literal"&&t.value===!0),idxFalse=types.findIndex(t=>t.type==="literal"&&t.value===!1);return idxTrue!==-1&&idxFalse!==-1&&(types.splice(Math.max(idxTrue,idxFalse),1),types.splice(Math.min(idxTrue,idxFalse),1,{type:"boolean"})),types.length>1?{type:"union",types}:types[0]}if(type.isIntersection())return{type:"intersection",types:type.types.map(t=>convertType(t,checker)).filter(t=>t!==void 0)}}function initializerToDefaultValue(expr,checker){if(import_typescript.default.isNumericLiteral(expr))return{text:expr.text};if(import_typescript.default.isStringLiteral(expr))return{text:JSON.stringify(expr.text)};if(import_typescript.default.isIdentifier(expr)||import_typescript.default.isPropertyAccessExpression(expr)){let symbol=checker.getSymbolAtLocation(expr);if(symbol&&checker.isUndefinedSymbol(symbol))return;let type=checker.getTypeAtLocation(expr);if(type.flags&import_typescript.default.TypeFlags.EnumLiteral)return{text:checker.typeToString(type)};if(type.isLiteral())return{text:JSON.stringify(type.value)};if(type.flags&import_typescript.default.TypeFlags.Null)return{text:"null"};if(type.flags&import_typescript.default.TypeFlags.BooleanLiteral)return{text:checker.typeToString(type)};if(type.getCallSignatures().length>0)return{text:"function"}}else{if(import_typescript.default.isArrayLiteralExpression(expr)||import_typescript.default.isObjectLiteralExpression(expr)||import_typescript.default.isNewExpression(expr))return{text:expr.getText()};if(import_typescript.default.isArrowFunction(expr))return{text:"function"}}switch(expr.kind){case import_typescript.default.SyntaxKind.TrueKeyword:return{text:"true"};case import_typescript.default.SyntaxKind.FalseKeyword:return{text:"false"};case import_typescript.default.SyntaxKind.NullKeyword:return{text:"null"}}return{text:"..."}}function loadConfig(basepath){let configPath=import_typescript.default.findConfigFile(basepath,import_typescript.default.sys.fileExists)||import_typescript.default.findConfigFile(basepath,import_typescript.default.sys.fileExists,"jsconfig.json"),forcedOptions={sourceMap:!1,noEmit:!0,strict:!0,allowJs:!0,checkJs:!0,skipLibCheck:!0,skipDefaultLibCheck:!0};if(!configPath)return[forcedOptions,new Set];let configFile=import_typescript.default.readConfigFile(configPath,import_typescript.default.sys.readFile),config=import_typescript.default.parseJsonConfigFileContent(configFile.config,import_typescript.default.sys,import_node_path.default.dirname(configPath),void 0,configPath,void 0,[{extension:"svelte",isMixedContent:!0,scriptKind:import_typescript.default.ScriptKind.Deferred}]),fileNames=new Set(config.fileNames.filter(fileName=>fileName.endsWith(".svelte")).map(fileName=>fileName+".tsx"));return[{...config.options,...forcedOptions},fileNames]}function createDocgenCache(){return{filenameToModifiedTime:{},filenameToSourceFile:{},fileCache:{}}}function generateDocgen(targetFileName,cache){targetFileName.endsWith(".svelte")&&(targetFileName=targetFileName+".tsx");let propsRuneUsed=!1;if(cache.options===void 0||!cache.rootNames?.has(targetFileName)){[cache.options,cache.rootNames]=loadConfig(targetFileName);let shimFilename=require.resolve("svelte2tsx/svelte-shims-v4.d.ts");cache.rootNames.add(shimFilename),cache.rootNames.add(targetFileName)}let originalHost=import_typescript.default.createCompilerHost(cache.options),host={...originalHost,readFile(fileName){let isCacheTarget=fileName.endsWith(import_node_path.default.sep+"package.json");if(isCacheTarget&&cache.fileCache[fileName])return cache.fileCache[fileName];let content=originalHost.readFile(fileName);return content&&isCacheTarget&&(cache.fileCache[fileName]=content),content},fileExists(fileName){if(fileName.endsWith(import_node_path.default.sep+"package.json")&&cache.fileCache[fileName])return!0;let exists=originalHost.fileExists(fileName);return exists||(fileName.endsWith(".svelte.tsx")||fileName.endsWith(".svelte.jsx")?(fileName=fileName.slice(0,-4),exists=originalHost.fileExists(fileName),exists):!1)},getSourceFile(fileName,languageVersion,onError){if(fileName.endsWith(".svelte.tsx")||fileName.endsWith(".svelte.jsx")){let realFileName=fileName.slice(0,-4),modifiedTime=import_typescript.default.sys.getModifiedTime?import_typescript.default.sys.getModifiedTime(realFileName):void 0;if(modifiedTime&&cache.filenameToModifiedTime[fileName]?.getTime()===modifiedTime.getTime())return cache.filenameToSourceFile[fileName];let content=originalHost.readFile(realFileName);if(content===void 0)return;let isTsFile=/<script\s+[^>]*?lang=('|")(ts|typescript)('|")/.test(content),tsx=import_svelte2tsx.default.svelte2tsx(content,{version:import_compiler.VERSION,isTsFile,mode:"dts"}),sourceFile2=import_typescript.default.createSourceFile(fileName,tsx.code,languageVersion,!0,isTsFile?import_typescript.default.ScriptKind.TS:import_typescript.default.ScriptKind.JS);return cache.filenameToSourceFile[fileName]=sourceFile2,cache.filenameToModifiedTime[fileName]=modifiedTime,sourceFile2}else{let staticCaching=!1;staticCaching||=fileName.split(import_node_path.default.sep).some(part=>part.toLowerCase()==="node_modules");let cachedSourceFile=cache.filenameToSourceFile[fileName];if(cachedSourceFile&&staticCaching)return cachedSourceFile;let modifiedTime=import_typescript.default.sys.getModifiedTime?import_typescript.default.sys.getModifiedTime(fileName):void 0;if(modifiedTime&&cache.filenameToModifiedTime[fileName]?.getTime()===modifiedTime.getTime())return cache.filenameToSourceFile[fileName];let content=originalHost.readFile(fileName);if(content===void 0)return;let sourceFile2=import_typescript.default.createSourceFile(fileName,content,languageVersion,!0);return cache.filenameToSourceFile[fileName]=sourceFile2,cache.filenameToModifiedTime[fileName]=modifiedTime,sourceFile2}},writeFile(){}},program=import_typescript.default.createProgram({rootNames:Array.from(cache.rootNames),options:cache.options,host,oldProgram:cache.oldProgram});cache.oldProgram=program;let checker=program.getTypeChecker(),sourceFile=program.getSourceFile(targetFileName);if(sourceFile===void 0)return{props:[]};let propMap=new Map,renderFunction=sourceFile.statements.find(statement=>import_typescript.default.isFunctionDeclaration(statement)&&statement.name?.text==="render");if(renderFunction===void 0)return{props:[]};let propsType,signature=checker.getSignatureFromDeclaration(renderFunction);return signature&&signature.declaration&&checker.getReturnTypeOfSignature(signature).getProperties().forEach(retObjProp=>{if(retObjProp.name==="props"){let decl=signature.getDeclaration();propsType=checker.getTypeOfSymbolAtLocation(retObjProp,decl),propsType.getProperties().forEach(prop=>{let name=prop.getName(),description=import_typescript.default.displayPartsToString(prop.getDocumentationComment(checker))||void 0,propType=checker.getTypeOfSymbolAtLocation(prop,decl);if(prop.valueDeclaration){let typeTag=import_typescript.default.getJSDocTypeTag(prop.valueDeclaration);typeTag?.comment&&(description=((description||"")+`
-`+typeTag.comment).trim())}if(prop.valueDeclaration?.getSourceFile().fileName.includes("node_modules/svelte/elements.d.ts"))return;let optional=(prop.flags&import_typescript.default.SymbolFlags.Optional)!==0;propMap.set(name,{name,optional,description,type:convertType(propType,checker)})})}}),renderFunction.body?.forEachChild(node=>{import_typescript.default.isVariableStatement(node)&&node.declarationList.declarations.forEach(declaration=>{if(import_typescript.default.isObjectBindingPattern(declaration.name)){let isPropsRune=declaration.initializer&&import_typescript.default.isCallExpression(declaration.initializer)&&import_typescript.default.isIdentifier(declaration.initializer.expression)&&declaration.initializer.expression.text==="$props",isPropsType=declaration.type&&propsType===checker.getTypeFromTypeNode(declaration.type);(isPropsRune||isPropsType)&&(propsRuneUsed=!0,declaration.name.elements.forEach(element=>{let name=element.name.getText(),prop=propMap.get(name);if(prop&&element.initializer){let defaultValue=initializerToDefaultValue(element.initializer,checker);defaultValue&&(prop.defaultValue=defaultValue)}}))}if(import_typescript.default.isVariableDeclaration(declaration)&&import_typescript.default.isIdentifier(declaration.name)&&propMap.has(declaration.name.text)){let prop=propMap.get(declaration.name.text);if(prop&&declaration.initializer){prop.optional=!0;let defaultValue=initializerToDefaultValue(declaration.initializer,checker);defaultValue&&(prop.defaultValue=defaultValue)}}})}),{props:Array.from(propMap.values()),propsRuneUsed}}var svelteDocParserOptions=require("sveltedoc-parser/lib/options.js");svelteDocParserOptions.getAstDefaultOptions=()=>({range:!0,loc:!0,comment:!0,tokens:!0,ecmaVersion:12,sourceType:"module",ecmaFeatures:{}});function getNameFromFilename(filename){if(!filename)return null;let parts=filename.split(/[/\\]/).map(encodeURI);if(parts.length>1){let indexMatch=parts[parts.length-1].match(/^index(\.\w+)/);indexMatch&&(parts.pop(),parts[parts.length-1]+=indexMatch[1])}let base=parts.pop()?.replace(/%/g,"u").replace(/\.[^.]+$/,"").replace(/[^a-zA-Z_$0-9]+/g,"_").replace(/^_/,"").replace(/_$/,"").replace(/^(\d)/,"_$1");if(!base)throw new Error(`Could not derive component name from file ${filename}`);return base[0].toUpperCase()+base.slice(1)}function transformToSvelteDocParserType(type){switch(type.type){case"string":return{kind:"type",type:"string",text:"string"};case"number":return{kind:"type",type:"number",text:"number"};case"boolean":return{kind:"type",type:"boolean",text:"boolean"};case"symbol":return{kind:"type",type:"other",text:"symbol"};case"null":return{kind:"type",type:"other",text:"null"};case"undefined":return{kind:"type",type:"other",text:"undefined"};case"void":return{kind:"type",type:"other",text:"void"};case"any":return{kind:"type",type:"any",text:"any"};case"object":return{kind:"type",type:"object",text:type.text};case"array":return{kind:"type",type:"array",text:type.text};case"function":return{kind:"function",text:type.text};case"literal":return{kind:"const",type:typeof type.value,value:type.value,text:type.text};case"union":{let nonNull=type.types.filter(t=>t.type!=="null"),text=nonNull.map(t=>transformToSvelteDocParserType(t).text).join(" | "),types=nonNull.map(t=>transformToSvelteDocParserType(t));return types.length===1?types[0]:{kind:"union",type:types,text}}case"intersection":return{kind:"type",type:"intersection",text:type.types.map(t=>transformToSvelteDocParserType(t).text).join(" & ")}}}function transformToSvelteDocParserDataItems(docgen){return docgen.props.map(p=>{let required=p.optional===!1&&p.defaultValue===void 0;return{name:p.name,visibility:"public",description:p.description,keywords:required?[{name:"required",description:""}]:[],kind:"let",type:p.type?transformToSvelteDocParserType(p.type):void 0,static:!1,readonly:!1,importPath:void 0,originalName:void 0,localName:void 0,defaultValue:p.defaultValue?p.defaultValue.text:void 0}})}async function svelteDocgen(svelteOptions={}){let cwd=process.cwd(),{preprocess:preprocessOptions,logDocgen=!1}=svelteOptions,include=/\.(svelte)$/,{createFilter}=await import("vite"),filter=createFilter(include),sourceFileCache=createDocgenCache(),docPreprocessOptions;return{name:"storybook:svelte-docgen-plugin",async transform(src,id){if(!filter(id))return;let resource=(0,import_node_path2.relative)(cwd,id),docgen=generateDocgen(resource,sourceFileCache),data=transformToSvelteDocParserDataItems(docgen),componentDoc={};if(!docgen.propsRuneUsed){if(preprocessOptions&&!docPreprocessOptions){docPreprocessOptions=[(0,import_svelte_preprocess.replace)([[/<style.+<\/style>/gims,""]])];try{require.resolve("typescript")&&docPreprocessOptions.unshift((0,import_svelte_preprocess.typescript)())}catch{}}let docOptions;if(docPreprocessOptions){let rawSource=(0,import_node_fs.readFileSync)(resource).toString(),{code:fileContent}=await(0,import_compiler2.preprocess)(rawSource,docPreprocessOptions,{filename:resource});docOptions={fileContent}}else docOptions={filename:resource};let options={...docOptions,version:3};try{componentDoc=await import_sveltedoc_parser.default.parse(options)}catch(error){componentDoc={keywords:[],data:[]},logDocgen&&import_node_logger.logger.error(error)}}componentDoc.data=data;let file=(0,import_node_path2.basename)(resource);componentDoc.name=(0,import_node_path2.basename)(file);let s=new import_magic_string.default(src),componentName=getNameFromFilename(resource);return s.append(`;${componentName}.__docgen = ${JSON.stringify(componentDoc)}`),{code:s.toString(),map:s.generateMap({hires:!0,source:id})}}}}var import_builder_vite=require("@storybook/builder-vite"),import_ts_dedent=require("ts-dedent");async function handleSvelteKit(plugins,options){let frameworkPreset=await options.presets.apply("framework",{},options),framework=typeof frameworkPreset=="string"?frameworkPreset:frameworkPreset.name;if(await(0,import_builder_vite.hasVitePlugins)(plugins,["vite-plugin-svelte-kit","vite-plugin-sveltekit-setup","vite-plugin-sveltekit-compile"])&&!framework.includes("@storybook/sveltekit"))throw new Error(import_ts_dedent.dedent`
+`+typeTag.comment).trim())}if(prop.valueDeclaration?.getSourceFile().fileName.includes("node_modules/svelte/elements.d.ts"))return;let optional=(prop.flags&import_typescript.default.SymbolFlags.Optional)!==0;propMap.set(name,{name,optional,description,type:convertType(propType,checker)})})}}),renderFunction.body?.forEachChild(node=>{import_typescript.default.isVariableStatement(node)&&node.declarationList.declarations.forEach(declaration=>{if(import_typescript.default.isObjectBindingPattern(declaration.name)){let isPropsRune=declaration.initializer&&import_typescript.default.isCallExpression(declaration.initializer)&&import_typescript.default.isIdentifier(declaration.initializer.expression)&&declaration.initializer.expression.text==="$props",isPropsType=declaration.type&&propsType===checker.getTypeFromTypeNode(declaration.type);(isPropsRune||isPropsType)&&(propsRuneUsed=!0,declaration.name.elements.forEach(element=>{let name=element.name.getText(),prop=propMap.get(name);if(prop&&element.initializer){let defaultValue=initializerToDefaultValue(element.initializer,checker);defaultValue&&(prop.defaultValue=defaultValue)}}))}if(import_typescript.default.isVariableDeclaration(declaration)&&import_typescript.default.isIdentifier(declaration.name)&&propMap.has(declaration.name.text)){let prop=propMap.get(declaration.name.text);if(prop&&declaration.initializer){prop.optional=!0;let defaultValue=initializerToDefaultValue(declaration.initializer,checker);defaultValue&&(prop.defaultValue=defaultValue)}}})}),{props:Array.from(propMap.values()),propsRuneUsed}}var svelteDocParserOptions=require("sveltedoc-parser/lib/options.js");svelteDocParserOptions.getAstDefaultOptions=()=>({range:!0,loc:!0,comment:!0,tokens:!0,ecmaVersion:12,sourceType:"module",ecmaFeatures:{}});function getNameFromFilename(filename){if(!filename)return null;let parts=filename.split(/[/\\]/).map(encodeURI);if(parts.length>1){let indexMatch=parts[parts.length-1].match(/^index(\.\w+)/);indexMatch&&(parts.pop(),parts[parts.length-1]+=indexMatch[1])}let base=parts.pop()?.replace(/%/g,"u").replace(/\.[^.]+$/,"").replace(/[^a-zA-Z_$0-9\+]+/g, '_').replace(/^_/,"").replace(/_$/,"").replace(/^\+/, '_').replace(/^(\d)/,"_$1");if(!base)throw new Error(`Could not derive component name from file ${filename}`);return base[0].toUpperCase()+base.slice(1)}function transformToSvelteDocParserType(type){switch(type.type){case"string":return{kind:"type",type:"string",text:"string"};case"number":return{kind:"type",type:"number",text:"number"};case"boolean":return{kind:"type",type:"boolean",text:"boolean"};case"symbol":return{kind:"type",type:"other",text:"symbol"};case"null":return{kind:"type",type:"other",text:"null"};case"undefined":return{kind:"type",type:"other",text:"undefined"};case"void":return{kind:"type",type:"other",text:"void"};case"any":return{kind:"type",type:"any",text:"any"};case"object":return{kind:"type",type:"object",text:type.text};case"array":return{kind:"type",type:"array",text:type.text};case"function":return{kind:"function",text:type.text};case"literal":return{kind:"const",type:typeof type.value,value:type.value,text:type.text};case"union":{let nonNull=type.types.filter(t=>t.type!=="null"),text=nonNull.map(t=>transformToSvelteDocParserType(t).text).join(" | "),types=nonNull.map(t=>transformToSvelteDocParserType(t));return types.length===1?types[0]:{kind:"union",type:types,text}}case"intersection":return{kind:"type",type:"intersection",text:type.types.map(t=>transformToSvelteDocParserType(t).text).join(" & ")}}}function transformToSvelteDocParserDataItems(docgen){return docgen.props.map(p=>{let required=p.optional===!1&&p.defaultValue===void 0;return{name:p.name,visibility:"public",description:p.description,keywords:required?[{name:"required",description:""}]:[],kind:"let",type:p.type?transformToSvelteDocParserType(p.type):void 0,static:!1,readonly:!1,importPath:void 0,originalName:void 0,localName:void 0,defaultValue:p.defaultValue?p.defaultValue.text:void 0}})}async function svelteDocgen(svelteOptions={}){let cwd=process.cwd(),{preprocess:preprocessOptions,logDocgen=!1}=svelteOptions,include=/\.(svelte)$/,{createFilter}=await import("vite"),filter=createFilter(include),sourceFileCache=createDocgenCache(),docPreprocessOptions;return{name:"storybook:svelte-docgen-plugin",async transform(src,id){if(!filter(id))return;let resource=(0,import_node_path2.relative)(cwd,id),docgen=generateDocgen(resource,sourceFileCache),data=transformToSvelteDocParserDataItems(docgen),componentDoc={};if(!docgen.propsRuneUsed){if(preprocessOptions&&!docPreprocessOptions){docPreprocessOptions=[(0,import_svelte_preprocess.replace)([[/<style.+<\/style>/gims,""]])];try{require.resolve("typescript")&&docPreprocessOptions.unshift((0,import_svelte_preprocess.typescript)())}catch{}}let docOptions;if(docPreprocessOptions){try{let rawSource=(0,import_node_fs.readFileSync)(resource).toString(),{code:fileContent}=await(0,import_compiler2.preprocess)(rawSource,docPreprocessOptions,{filename:resource});docOptions={fileContent}}catch(e){console.warn("svelte-docgen-plugin failed to read and preprocess \"" + resource + "\", skipping");console.log(e);}}else docOptions={filename:resource};let options={...docOptions,version:3};try{componentDoc=await import_sveltedoc_parser.default.parse(options)}catch(error){componentDoc={keywords:[],data:[]},logDocgen&&import_node_logger.logger.error(error)}}componentDoc.data=data;let file=(0,import_node_path2.basename)(resource);componentDoc.name=(0,import_node_path2.basename)(file);let s=new import_magic_string.default(src),componentName=getNameFromFilename(resource);return s.append(`;${componentName}.__docgen = ${JSON.stringify(componentDoc)}`),{code:s.toString(),map:s.generateMap({hires:!0,source:id})}}}}var import_builder_vite=require("@storybook/builder-vite"),import_ts_dedent=require("ts-dedent");async function handleSvelteKit(plugins,options){let frameworkPreset=await options.presets.apply("framework",{},options),framework=typeof frameworkPreset=="string"?frameworkPreset:frameworkPreset.name;if(await(0,import_builder_vite.hasVitePlugins)(plugins,["vite-plugin-svelte-kit","vite-plugin-sveltekit-setup","vite-plugin-sveltekit-compile"])&&!framework.includes("@storybook/sveltekit"))throw new Error(import_ts_dedent.dedent`
       We've detected a SvelteKit project using the @storybook/svelte-vite framework, which is not supported in Storybook 7.0
       Please use the @storybook/sveltekit framework instead.
       You can migrate automatically by running
diff --git a/template/stories_svelte-vite-default-ts/docs-ts.stories.js b/template/stories_svelte-vite-default-ts/docs-ts.stories.js
deleted file mode 100644
index 0124aa9b58f47b2d6f1acbe68ea687fde6de7c42..0000000000000000000000000000000000000000
