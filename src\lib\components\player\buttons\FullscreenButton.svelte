<script lang="ts">
	import Tooltip from "../Tooltip.svelte";
	import type { FullscreenButtonProps } from "$lib/models/props/fullscreenButtonProps";
	import FullscreenIcon from "~icons/media-icons/fullscreen";
	import FullscreenExitIcon from "~icons/media-icons/fullscreen-exit";

	let { tooltipPlacement }: FullscreenButtonProps = $props();
</script>

<Tooltip placement={tooltipPlacement}>
	{#snippet trigger()}
		<media-fullscreen-button
			class="ring-media-focus group relative inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg outline-none ring-inset hover:bg-foreground/20 data-[focus]:ring-4 aria-hidden:hidden"
		>
			<FullscreenIcon class="media-fullscreen:hidden h-8 w-8"></FullscreenIcon>
			<FullscreenExitIcon class="media-fullscreen:block hidden h-8 w-8"></FullscreenExitIcon>
		</media-fullscreen-button>
	{/snippet}

	{#snippet content()}
		<span class="media-fullscreen:hidden">Enter Fullscreen</span>
		<span class="media-fullscreen:block hidden">Exit Fullscreen</span>
	{/snippet}
</Tooltip>
