<script lang="ts">
	import Tooltip from "../Tooltip.svelte";
	import type { MuteButtonProps } from "$lib/models/props/MuteButtonProps";
	import PlayIcon from "~icons/media-icons/play";
	import PauseIcon from "~icons/media-icons/pause";

	let { tooltipPlacement }: MuteButtonProps = $props();
</script>

<Tooltip placement={tooltipPlacement}>
	{#snippet trigger()}
		<media-play-button
			class="ring-media-focus relative inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg outline-none ring-inset hover:bg-foreground/20 data-[focus]:ring-4"
		>
			<PlayIcon class="media-paused:block hidden h-8 w-8" />
			<PauseIcon class="media-paused:hidden h-8 w-8" />
		</media-play-button>
	{/snippet}

	{#snippet content()}
		<span class="media-paused:block hidden">Play</span>
		<span class="media-paused:hidden">Pause</span>
	{/snippet}
</Tooltip>
