<script lang="ts">
	import type { RoleIconProps } from "$lib/models/props/roleIconProps";
	import { RoomUserRoles } from "$lib/models/roomUser";
	import CrownSimpleFillIcon from "~icons/phosphor-icons-fill/crown-simple-fill";
	import ShieldStarFillIcon from "~icons/phosphor-icons-fill/shield-star-fill";
	import ShieldFillIcon from "~icons/phosphor-icons-fill/shield-fill";
	import EyeIcon from "~icons/phosphor-icons-fill/eye-fill";

	const DEFAULT_ICON_SIZE = 5;

	let { role, size = DEFAULT_ICON_SIZE, colorClass }: RoleIconProps = $props();
</script>

{#if role === RoomUserRoles.Guest}
	<EyeIcon class="size-{size} {colorClass ? colorClass : 'text-role-guest'}" />
{:else if role === RoomUserRoles.Moderator}
	<ShieldFillIcon class="size-{size} {colorClass ? colorClass : 'text-role-moderator'}" />
{:else if role === RoomUserRoles.Administrator}
	<ShieldStarFillIcon class="size-{size} {colorClass ? colorClass : 'text-role-administrator'}" />
{:else if role === RoomUserRoles.Creator}
	<CrownSimpleFillIcon class="size-{size} {colorClass ? colorClass : 'text-role-creator'}" />
{/if}
