import type { RoomUser } from "$lib/models/roomUser";
import { getContext, setContext } from "svelte";

const USER_KEY = Symbol("WATCH_APP_USER");

export class CurrentRoomUserState {
	user: RoomUser | undefined = $state();

	constructor(user?: RoomUser) {
		this.user = user;
	}
}

export function setCurrentRoomUserState(user?: RoomUser): CurrentRoomUserState {
	return setContext(USER_KEY, new CurrentRoomUserState(user));
}

export function getCurrentRoomUserState(): ReturnType<typeof setCurrentRoomUserState> {
	return getContext<ReturnType<typeof setCurrentRoomUserState>>(USER_KEY);
}
