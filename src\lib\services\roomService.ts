import type { GetRoomResponse } from "$lib/models/dto/responses/getRoomResponse";
import { NotFoundError } from "$lib/models/result/notFoundError";
import { fail, ok, type ValueResult } from "$lib/models/result/result";
import { ResultError } from "$lib/models/result/resultError";
import { UnauthorizedError } from "$lib/models/result/unauthorizedError";
import { StatusCodes } from "$lib/utils/statusCodes";
import { ApiService } from "./apiService";

export class RoomService {
	private static instance: RoomService;

	private readonly apiService: ApiService;

	public constructor(apiService: ApiService = ApiService.getInstance()) {
		this.apiService = apiService;
	}

	public static getInstance(): RoomService {
		if (!RoomService.instance) {
			RoomService.instance = new RoomService();
		}

		return RoomService.instance;
	}

	public async getRoom(roomIdHash: string): Promise<ValueResult<GetRoomResponse>> {
		const apiResponse = await this.apiService.getRoom(roomIdHash);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.UNPROCESSABLE_ENTITY:
					error = new NotFoundError("Room id is invalid").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<GetRoomResponse>(error);
				case StatusCodes.NOT_FOUND:
					error = new NotFoundError("Room is not found").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<GetRoomResponse>(error);
				default:
					error = new ResultError("Failed to get room info").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<GetRoomResponse>(error);
			}
		}

		const room = apiResponse.body;
		return ok(room);
	}

	public async getSvg(url: string): Promise<ValueResult<string>> {
		const apiResponse = await this.apiService.getSvg(url);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.NOT_FOUND:
					error = new NotFoundError("Svg is not found").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<string>(error);
				default:
					error = new ResultError("Failed to get svg").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<string>(error);
			}
		}

		const svg = apiResponse.body;
		return ok(svg);
	}

	public async createRoom(userToken: string): Promise<ValueResult<string>> {
		const apiResponse = await this.apiService.createRoom(userToken);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.UNAUTHORIZED:
					error = new UnauthorizedError("User is unauthorized").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<string>(error);
				default:
					error = new ResultError(
						`${apiResponse.statusCode}: ${apiResponse.statusText}`
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<string>(error);
			}
		}

		const idHash: string = apiResponse.body.idHash;
		return ok(idHash);
	}

	public async joinRoom(roomIdHash: string, userToken: string): Promise<ValueResult<string>> {
		const apiResponse = await this.apiService.joinRoom(roomIdHash, userToken);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.UNAUTHORIZED:
					error = new UnauthorizedError("User is unauthorized").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<string>(error);
				case StatusCodes.UNPROCESSABLE_ENTITY:
					error = new NotFoundError("Room id is invalid").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<string>(error);
				case StatusCodes.NOT_FOUND:
					error = new NotFoundError("Room doesn't exist").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<string>(error);
				default:
					error = new ResultError(
						`${apiResponse.statusCode}: ${apiResponse.statusText}`
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<string>(error);
			}
		}

		const result = "";
		return ok(result);
	}
}
