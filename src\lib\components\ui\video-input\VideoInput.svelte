<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import type { VideoInputProps } from "$lib/models/props/videoInputProps";
	import { getAddVideoEvent } from "$lib/states/videoInputState.svelte";
	import { isValidHttpUrl } from "$lib/utils/isValidHttpUrl";

	let { videoUrl, onClickAdd }: VideoInputProps = $props();
	let inputValue = $state(videoUrl ?? "");
	let addVideoEvent = getAddVideoEvent();

	function handleClickAdd(value: string): void {
		onClickAdd?.(value);
		addVideoEvent.emit(value);
	}
</script>

<div class="flex space-x-2">
	<Input
		class="shadows-box-level0"
		type="text"
		placeholder="Enter video link"
		bind:value={inputValue}
	></Input>
	<Button
		class="shadows-button-bright-level0"
		disabled={!isValidHttpUrl(inputValue)}
		onclick={() => handleClickAdd(inputValue)}><span class="pb-[1px]">Add</span></Button
	>
</div>
