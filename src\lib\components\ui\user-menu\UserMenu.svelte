<script lang="ts">
	import * as Avatar from "$lib/components/ui/avatar";
	import * as Popover from "$lib/components/ui/popover";
	import ChevronDownIcon from "~icons/media-icons/chevron-down";
	import DiceThreeFillIcon from "~icons/phosphor-icons-fill/dice-three-fill";
	import PencilSimpleLineFillIcon from "~icons/phosphor-icons-fill/pencil-simple-line-fill";
	import UserSwitchFillIcon from "~icons/phosphor-icons-fill/user-switch-fill";
	import Spinner from "$lib/components/ui/spinner/Spinner.svelte";
	import { fade, slide } from "svelte/transition";
	import { getAppUserState, AppUserState } from "$lib/states/appUserState.svelte";
	import type { AppUser } from "$lib/models/appUser";
	import UserCard from "$lib/components/ui/user-card/UserCard.svelte";
	import { Button } from "$lib/components/ui/button";
	import { UserAvatarService } from "$lib/services/userAvatarService";

	const userAvatarService = UserAvatarService.getInstance();
	const userState: AppUserState = getAppUserState();
	let user: AppUser | undefined = $derived(userState.user);
	let isOpen: boolean = $state(false);

	function handleClickRerollUsername() {
		isOpen = false;
		userState.rerollUsernameSignal.emit(userState);
	}

	function handleClickChangeUsername() {
		isOpen = false;
		userState.changeUsernameSignal.emit(userState);
	}

	function handleClickRecoverUsername() {
		isOpen = false;
		userState.recoverUsernameSignal.emit(userState);
	}
</script>

<Popover.Root bind:open={isOpen}>
	<Popover.Trigger
		disabled={!user}
		data-testid="btn-user-menu"
		class="focus-visible:outline outline-2 outline-primary/75 outline-offset-2 rounded-lg"
	>
		<div
			class="relative bg-accent text-accent-foreground inline-flex items-center justify-center whitespace-nowrap shadows-button-level0 rounded-lg text-sm font-medium transition-colors {user
				? 'button'
				: ''} h-10 px-4 py-2"
		>
			<div class="grid justify-items-start items-center py-2">
				{#if !user}
					<div
						class="col-start-1 col-end-2 row-start-1 row-end-2"
						transition:fade={{ duration: 250 }}
					>
						<Spinner size={28}></Spinner>
					</div>
				{:else}
					<div
						class="col-start-1 col-end-2 row-start-1 row-end-2"
						transition:fade={{ duration: 250 }}
					>
						<div class="flex space-x-2 items-center">
							<div>
								<Avatar.Root class="h-7 w-7">
									<Avatar.Image
										src={userAvatarService.getAvatarUrl(user?.username)}
										alt={user.username}
									></Avatar.Image>
									<Avatar.Fallback class="pb-[1px]">{user?.username[0]}</Avatar.Fallback>
								</Avatar.Root>
							</div>
							<div
								class="flex items-center space-x-2"
								transition:slide={{ axis: "x", duration: 250 }}
							>
								<span class="pb-[1px]">{user?.username}</span>
								<ChevronDownIcon class="size-4"></ChevronDownIcon>
							</div>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</Popover.Trigger>
	<Popover.Content>
		{#if user}
			<UserCard {user}></UserCard>
			<div class="flex flex-col gap-y-2 mt-4">
				<Button
					class="shadows-button-level1"
					variant="secondary"
					onclick={handleClickRerollUsername}
					><DiceThreeFillIcon class="size-5"></DiceThreeFillIcon><span>Reroll</span></Button
				>
				<Button
					class="shadows-button-level1"
					variant="secondary"
					onclick={handleClickChangeUsername}
					><PencilSimpleLineFillIcon class="size-5"></PencilSimpleLineFillIcon><span
						>Change name</span
					></Button
				>
				<Button
					class="shadows-button-level1"
					variant="secondary"
					onclick={handleClickRecoverUsername}
					><UserSwitchFillIcon class="size-5"></UserSwitchFillIcon><span>Recover</span></Button
				>
			</div>
		{/if}
	</Popover.Content>
</Popover.Root>
