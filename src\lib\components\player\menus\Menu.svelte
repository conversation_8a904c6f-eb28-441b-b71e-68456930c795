<script lang="ts">
	import Tooltip from "../Tooltip.svelte";
	import type { MenuProps } from "$lib/models/props/menuProps";

	let { placement, tooltipPlacement, button, tooltipContent, content }: MenuProps = $props();
</script>

<media-menu>
	<!-- Menu <PERSON>ton -->
	<Tooltip placement={tooltipPlacement}>
		{#snippet trigger()}
			<media-menu-button
				class="group ring-media-focus relative mr-0.5 inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg outline-none ring-inset hover:bg-foreground/20 data-[focus]:ring-4 aria-hidden:hidden"
			>
				{@render button()}
			</media-menu-button>
		{/snippet}

		{#snippet content()}
			{@render tooltipContent()}
		{/snippet}
	</Tooltip>
	<!-- Menu Items -->
	<media-menu-items
		class="animate-out fade-out slide-out-to-bottom-2 data-[open]:animate-in data-[open]:fade-in data-[open]:slide-in-from-bottom-4 flex h-[var(--menu-height)] max-h-[400px] !w-min max-w-[260px] min-w-[260px] flex-col overflow-y-auto overscroll-y-contain rounded-xl border border-foreground/10 bg-player-neutral-950 p-2.5 font-sans text-[15px] font-medium outline-none transition-[height] duration-300 will-change-[height] data-[resizing]:overflow-hidden"
		{placement}
	>
		{@render content()}
	</media-menu-items>
</media-menu>
