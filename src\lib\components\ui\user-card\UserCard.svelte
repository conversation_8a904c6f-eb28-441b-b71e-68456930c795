<script lang="ts">
	import type { UserCardProps } from "$lib/models/props/userCardProps";
	import * as Avatar from "$lib/components/ui/avatar";
	import { RoomUserRoles } from "$lib/models/roomUser";
	import RoleIcon from "$lib/components/ui/role-icon/RoleIcon.svelte";
	import { UserAvatarService } from "$lib/services/userAvatarService";

	let { user }: UserCardProps = $props();

	const userAvatarService = UserAvatarService.getInstance();
</script>

<div class="flex flex-col justify-center items-center">
	<Avatar.Root class="size-16">
		<Avatar.Image src={userAvatarService.getAvatarUrl(user.username)} alt={user.username}
		></Avatar.Image>
		<Avatar.Fallback>{user.username[0]}</Avatar.Fallback>
	</Avatar.Root>
	<span class="mt-1 font-medium">{user.username}</span>
	{#if "role" in user}
		<div class="flex items-center justify-center">
			<span class="text-xs text-player-neutral-300">{RoomUserRoles[user.role]}</span>
			<span class="ml-1"><RoleIcon role={user.role} size={4}></RoleIcon></span>
		</div>
	{/if}
</div>
