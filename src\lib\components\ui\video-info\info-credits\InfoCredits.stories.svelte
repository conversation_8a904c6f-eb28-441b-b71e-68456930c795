<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import InfoCredits from "./InfoCredits.svelte";

	const { Story } = defineMeta({
		title: "Components/VideoInfo/InfoCredits",
		component: InfoCredits
	});
</script>

<Story name="Primary" args={{ credits: { actors: [{ name: "Actor" }] } }} />

<Story
	name="WithActorsAndEmptyDirectors"
	args={{ credits: { actors: [{ name: "Actor" }], directors: [] } }}
/>

<Story
	name="WithMultipleActors"
	args={{
		credits: {
			actors: [
				{ name: "Actor", order: 0 },
				{ name: "Actor Without Order" },
				{ name: "Actor 3", order: 100 }
			]
		}
	}}
/>

<Story
	name="WithFullCredits"
	args={{
		credits: {
			guestActors: [
				{
					character: "<PERSON>",
					order: 5,
					name: "<PERSON><PERSON>",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/zKUWYrROoa9RvxAggbolQRaSLVG.jpg"
				},
				{
					character: "<PERSON><PERSON><PERSON>",
					order: 505,
					name: "<PERSON>",
					profileImageUrl: "https://image.tmdb.org/t/p/w185None"
				},
				{
					character: "Delivery Guy / Meat Delivery Guy",
					order: 506,
					name: "PJ Fishwick",
					profileImageUrl: "https://image.tmdb.org/t/p/w185None"
				},
				{
					character: "Evil Carrot",
					order: 507,
					name: "Patrick Dunham",
					profileImageUrl: "https://image.tmdb.org/t/p/w185None"
				},
				{
					character: "Phil Ramis",
					order: 508,
					name: "Greg Poljacik",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/djTxAdMAQwCAln8bpIxZyVpGKjK.jpg"
				},
				{
					character: "Robot",
					order: 509,
					name: "Chris McClure",
					profileImageUrl: "https://image.tmdb.org/t/p/w185None"
				},
				{
					character: "Nerd 1",
					order: 510,
					name: "Aaron Crippen",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/6WKNKhiTtCysGf0VwLvTBdGZZcD.jpg"
				},
				{
					character: "Nerd 2",
					order: 511,
					name: "Kai Young",
					profileImageUrl: "https://image.tmdb.org/t/p/w185None"
				},
				{
					character: "Nerd 3",
					order: 512,
					name: "Will Bennett",
					profileImageUrl: "https://image.tmdb.org/t/p/w185None"
				},
				{
					character: "Ebraheim",
					order: 536,
					name: "Edwin Lee Gibson",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/wyDPu35soTnBi4VClpS8fv2atkE.jpg"
				},
				{
					character: "Gary 'Sweeps' Woods",
					order: 537,
					name: "Corey Hendrix",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/jurlf1Ot0mofDsoFTqIBrezx68t.jpg"
				},
				{
					character: "Angel",
					order: 538,
					name: "Jose M. Cervantes",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/yubXHfuuPfqbrZ3PpEegDwwbGaa.jpg"
				},
				{
					character: "Manny",
					order: 539,
					name: "Richard Esteras",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/kepvENRqMTNxdy2Gz9Hch1SJyVc.jpg"
				}
			],
			creators: [
				{
					name: "Christopher Storer",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/kG6I1GlaUtENJoJzrP7uxiJovy9.jpg"
				}
			],
			actors: [
				{
					character: "Carmen 'Carmy' Berzatto",
					order: 0,
					name: "Jeremy Allen White",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/zKk4vmDeUexdevtt6wm8WdMQ1TG.jpg"
				},
				{
					character: "Richard 'Richie' Jerimovich",
					order: 1,
					name: "Ebon Moss-Bachrach",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/xD8GVNayMpiTZxLfahy2DseYcQq.jpg"
				},
				{
					character: "Sydney Adamu",
					order: 2,
					name: "Ayo Edebiri",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/V9TNVjNkAJIiCHLTzcnHLktnPf.jpg"
				},
				{
					character: "Marcus Brooks",
					order: 3,
					name: "Lionel Boyce",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/hpIxX5nkfA3pWCW8rYkEUCSBVyS.jpg"
				},
				{
					character: "Natalie 'Sugar' Berzatto",
					order: 4,
					name: "Abby Elliott",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/v7h08EsTce2pLHkZiaFe1QRuYbU.jpg"
				},
				{
					character: "Bettina 'Tina' Marrero",
					order: 6,
					name: "Liza Colón-Zayas",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/uuFxdf0SlKQfSoZQmEAWYMTyz3e.jpg"
				}
			],
			writers: [
				{
					name: "Christopher Storer",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/kG6I1GlaUtENJoJzrP7uxiJovy9.jpg"
				}
			],
			directors: [
				{
					name: "Christopher Storer",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/kG6I1GlaUtENJoJzrP7uxiJovy9.jpg"
				}
			],
			originalMusicComposers: [
				{
					name: "J.A.Q.",
					profileImageUrl: "https://image.tmdb.org/t/p/w185/mQ4BT0Xsyj4TQVhXzQGFyq5pcaD.jpg"
				}
			]
		}
	}}
/>
