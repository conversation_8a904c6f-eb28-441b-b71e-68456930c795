import IntervalRunnerWorker from './intervalRunner.worker?worker';
import type {
	ResponseMessage,
	StartRequestMessage,
	StopRequestMessage
} from './intervalRunner.worker';

export interface IntervalRunnerContext {
	isCancellationRequested?: boolean;
	isCompletionRequested?: boolean;
	isCompleted?: boolean;
	isCancelled?: boolean;
}

export interface IntervalRunnerArguments<TContext extends IntervalRunnerContext> {
	onBeforeStart?: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	onElapsed: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	onCancelled?: (context: TContext) => void;
	onCompleted?: (context: TContext) => void;
	intervalMs: number;
	context: TContext;
}

export class IntervalRunner<TContext extends IntervalRunnerContext> {
	private _onBeforeStart?: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	private _onElapsed: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	private _onCancelled?: (context: TContext) => void;
	private _onCompleted?: (context: TContext) => void;
	private _context: TContext;
	private _intervalMs: number;
	private _isOnCancelledRun: boolean;
	private _isOnCompletedRun: boolean;
	private _isOnBeforeRun: boolean;
	private _workerId: string;
	private _intervalRunnerWorker: Worker;

	public constructor(args: IntervalRunnerArguments<TContext>) {
		this._onBeforeStart = args.onBeforeStart;
		this._onElapsed = args.onElapsed;
		this._onCancelled = args.onCancelled;
		this._onCompleted = args.onCompleted;
		this._context = args.context;
		this._intervalMs = args.intervalMs;
		this._isOnCancelledRun = false;
		this._isOnCompletedRun = false;
		this._isOnBeforeRun = false;
		this._workerId = crypto.randomUUID();

		this._intervalRunnerWorker = new IntervalRunnerWorker();
		this._intervalRunnerWorker.onmessage = this._onIntervalWorkerMessage.bind(this);
	}

	public async start(): Promise<void> {
		const startMessage: StartRequestMessage = {
			type: 'start',
			intervalId: this._workerId,
			intervalMs: this._intervalMs,
			immediateExecution: true
		};

		this._intervalRunnerWorker.postMessage(startMessage);
	}

	public cancel(): void {
		this._stopIntervalRunnerWorker();

		this._handleCancelled();
		this._handleCompleted();
	}

	private _elapsed(timestamp: DOMHighResTimeStamp): void {
		if (this._context.isCancelled || this._context.isCompleted) {
			return;
		}

		if (this._context.isCancellationRequested) {
			this._stopIntervalRunnerWorker();

			this._handleCancelled();
			this._handleCompleted();

			return;
		}

		if (this._context.isCompletionRequested) {
			this._stopIntervalRunnerWorker();

			this._handleCompleted();

			return;
		}

		if (!this._isOnBeforeRun) {
			this._isOnBeforeRun = true;
			this._onBeforeStart?.(this._context, timestamp);
		} else {
			this._onElapsed(this._context, timestamp);
		}
	}

	private _stopIntervalRunnerWorker(): void {
		const stopMessage: StopRequestMessage = {
			type: 'stop',
			intervalId: this._workerId
		};

		this._intervalRunnerWorker.postMessage(stopMessage);
	}

	private _handleCancelled(): void {
		this._context.isCancelled = true;

		if (this._onCancelled && !this._isOnCancelledRun) {
			this._isOnCancelledRun = true;
			this._onCancelled(this._context);
		}
	}

	private _handleCompleted(): void {
		this._context.isCompleted = true;

		if (this._onCompleted && !this._isOnCompletedRun) {
			this._isOnCompletedRun = true;
			this._onCompleted(this._context);
		}
	}

	private _onIntervalWorkerMessage(messageEvent: MessageEvent<ResponseMessage>) {
		const message = messageEvent.data;
		switch (message.type) {
			case 'elapsed':
				if (message.intervalId !== this._workerId) {
					break;
				}

				this._elapsed(message.timestamp);
				break;
		}
	}
}
