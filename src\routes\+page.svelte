<script lang="ts">
	import { goto } from "$app/navigation";
	import { Button, buttonVariants } from "$lib/components/ui/button";
	import { UnauthorizedError } from "$lib/models/result/unauthorizedError";
	import { RoomService } from "$lib/services/roomService";
	import * as Dialog from "$lib/components/ui/dialog";
	import { Label } from "$lib/components/ui/label";
	import { Input } from "$lib/components/ui/input";
	import { page } from "$app/stores";
	import Spinner from "$lib/components/ui/spinner/Spinner.svelte";
	import { getAppUserState } from "$lib/states/appUserState.svelte";
	import { AppUserService } from "$lib/services/appUserService";
	import { CancellationToken, retryResult } from "$lib/utils/retry";
	import { onDestroy, onMount } from "svelte";
	import { CancelledError } from "$lib/models/result/cancelledError";
	import { displayError } from "$lib/utils/toastUtils";

	const CREATE_SPINNER_DELAY = 250;
	const MAX_CREATE_ROOM_RETRIES = Number.POSITIVE_INFINITY;
	const CREATE_ROOM_RETRY_DELAY = 1000;
	const JOIN_MODAL_SEARCH_PARAM = "join";

	const roomService = RoomService.getInstance();
	const appUserService = AppUserService.getInstance();
	const appUserState = getAppUserState();
	const createRoomCt: CancellationToken = new CancellationToken();

	let roomCode = $state("");
	let currentUser = $derived(appUserState.user);
	let hasJoinParam = $derived($page.url.searchParams.has(JOIN_MODAL_SEARCH_PARAM));
	let isCreateSpinnerVisible: boolean = $state(false);
	let isCreatingRoom: boolean = false;
	let createRoomSpinnerTimeout: NodeJS.Timeout | undefined;

	onMount(() => {
		appUserService.waitForInitializedUser();
	});

	onDestroy(() => {
		createRoomCt.cancel();
		appUserService.waitForUserCt?.cancel();
	});

	async function createRoom(): Promise<void> {
		let userToken: string;

		if (currentUser) {
			userToken = currentUser.token;
		} else {
			const appUser = await appUserService.waitForInitializedUser();
			userToken = appUser.token;
		}

		const createRoomResult = await retryResult<string>(
			() => roomService.createRoom(userToken),
			MAX_CREATE_ROOM_RETRIES,
			CREATE_ROOM_RETRY_DELAY,
			async (error) => {
				displayError("Failed to a create room", error, true);
				if (error instanceof UnauthorizedError) {
					appUserService.removeCurrentUser();
					const appUser = await appUserService.waitForInitializedUser();
					userToken = appUser.token;
				}
			},
			createRoomCt
		);

		if (createRoomResult.isFailed) {
			if (createRoomResult.error instanceof CancelledError) {
				return;
			}

			displayError("Failed to create a room", createRoomResult.error);
			return;
		}

		const roomIdHash: string = createRoomResult.value;
		goto(`/${roomIdHash}`, { replaceState: false });
	}

	async function onClickCreateRoom(): Promise<void> {
		if (isCreatingRoom) {
			return;
		}

		isCreatingRoom = true;
		startCreateSpinnerTimeout();
		await createRoom();
		isCreatingRoom = false;
	}

	function startCreateSpinnerTimeout() {
		clearTimeout(createRoomSpinnerTimeout);
		createRoomSpinnerTimeout = setTimeout(
			() => (isCreateSpinnerVisible = true),
			CREATE_SPINNER_DELAY
		);
	}

	function stopCreateSpinnerTimeout() {
		clearTimeout(createRoomSpinnerTimeout);
	}

	function onOpenChangeJoinDialog(open: boolean): void {
		if (open) {
			const params = new URLSearchParams($page.url.searchParams.toString());
			params.set(JOIN_MODAL_SEARCH_PARAM, "");
			goto(`?${params.toString()}`, { replaceState: false });
		} else {
			const entriesWithoutJoinParam = Array.from($page.url.searchParams.entries()).filter(
				([k, _]) => k !== JOIN_MODAL_SEARCH_PARAM
			);
			const params = new URLSearchParams(entriesWithoutJoinParam);
			goto(`?${params.toString()}`, { replaceState: false });
		}
	}
</script>

<svelte:head>
	<meta property="og:site_name" content="watch" />
	<title>watch</title>
</svelte:head>

<div class="flex flex-col justify-center items-center content-buttons">
	<Button class="w-36 mt-[-5rem] mb-2 shadows-button-bright-level0" onclick={onClickCreateRoom}
		>{#if isCreateSpinnerVisible}
			<Spinner innerCircleColor="#DA448D"></Spinner>
		{:else}
			<span class="pb-[1px]">Create</span>
		{/if}</Button
	>

	<Dialog.Root open={hasJoinParam} onOpenChange={onOpenChangeJoinDialog}>
		<Dialog.Trigger
			class="{buttonVariants({ variant: 'outline' })} w-36 shadows-button-ghost-level0 border-none"
			><span class="pb-[1px]">Join</span></Dialog.Trigger
		>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>Join a room</Dialog.Title>
				<Dialog.Description>Enter a room code to join an already existing room.</Dialog.Description>
			</Dialog.Header>

			<div class="flex flex-col">
				<form class="flex flex-col w-full space-y-2">
					<Label for="room_code">Room code</Label>
					<Input
						id="room_code"
						type="text"
						class="shadows-box-level1"
						placeholder="Enter a room code"
						required
						bind:value={roomCode}
					></Input>
				</form>
			</div>

			<Dialog.Footer>
				<Button href={`/${roomCode}`} class="shadows-button-bright-level1"
					><span class="pb-[1px]">Join room</span></Button
				>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</div>

<style>
	.content-buttons {
		min-height: calc(100vh - 5rem);
	}
</style>
