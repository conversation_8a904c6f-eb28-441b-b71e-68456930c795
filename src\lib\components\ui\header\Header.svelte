<script lang="ts">
	import Logo from "$lib/components/ui/logo/Logo.svelte";
	import UserMenu from "$lib/components/ui/user-menu/UserMenu.svelte";
	import VideoInput from "$lib/components/ui/video-input/VideoInput.svelte";
	import { getHeaderState, HeaderState } from "$lib/states/headerState.svelte";

	const headerState: HeaderState = getHeaderState();
	let isVideoInputVisible = $derived(headerState.isVideoInputVisible);
</script>

<header class="flex flex-col px-4 py-2">
	<div class="flex items-center justify-between">
		<div class="grow basis-0"><a href="/"><Logo></Logo></a></div>

		{#if isVideoInputVisible}
			<div class="hidden sm:block"><VideoInput></VideoInput></div>
		{/if}

		<div class="grow basis-0 flex justify-end"><UserMenu></UserMenu></div>
	</div>

	{#if isVideoInputVisible}
		<div class="block sm:hidden"><VideoInput></VideoInput></div>
	{/if}
</header>
