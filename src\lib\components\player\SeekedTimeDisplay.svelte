<script lang="ts">
	import { onDestroy } from "svelte";
	import SeekForwardIcon from "~icons/media-icons/seek-forward";
	import SeekBackwardIcon from "~icons/media-icons/seek-backward";

	const RESET_SEEKED_TIME_MS = 1000;

	let hideSeekedDisplayTimeout: NodeJS.Timeout;
	let seekedSeconds: number = $state(0);

	export function addSeekedSeconds(seconds: number): void {
		seekedSeconds += seconds;
		clearTimeout(hideSeekedDisplayTimeout);
		hideSeekedDisplayTimeout = setTimeout(() => {
			seekedSeconds = 0;
		}, RESET_SEEKED_TIME_MS);
	}

	export function resetSeekedSeconds(): void {
		seekedSeconds = 0;
	}

	onDestroy(() => {
		clearTimeout(hideSeekedDisplayTimeout);
	});
</script>

<div
	class="absolute inset-0 pointer-events-none z-0 text-accent-foreground transition-opacity duration-200 {seekedSeconds ===
	0
		? 'opacity-0'
		: 'opacity-100'}"
>
	<div class="flex h-full">
		<div class="flex-1 {seekedSeconds < 0 ? 'hidden' : ''}"></div>
		<div class="flex items-center px-36 {seekedSeconds === 0 ? 'hidden' : ''}">
			<div
				class="flex flex-col justify-center items-center w-28 h-28 bg-player-neutral-950/50 rounded-full"
			>
				<SeekForwardIcon class="h-8 w-8 {seekedSeconds < 0 ? 'hidden' : ''}"></SeekForwardIcon>
				<SeekBackwardIcon class="h-8 w-8 {seekedSeconds > 0 ? 'hidden' : ''}"></SeekBackwardIcon>
				<span class="text-sm">{Math.abs(seekedSeconds)} seconds</span>
			</div>
		</div>
		<div class="flex-1 {seekedSeconds > 0 ? 'hidden' : ''}"></div>
	</div>
</div>
