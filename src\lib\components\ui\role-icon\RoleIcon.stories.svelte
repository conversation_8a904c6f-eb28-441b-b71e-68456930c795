<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import RoleIcon from "./RoleIcon.svelte";
	import { RoomUserRoles } from "$lib/models/roomUser";

	const { Story } = defineMeta({
		title: "Components/RoleIcon",
		component: RoleIcon,
		argTypes: {
			role: {
				control: "object"
			}
		}
	});
</script>

<Story name="Guest" args={{ role: RoomUserRoles.Guest }}></Story>

<Story name="Viewer" args={{ role: RoomUserRoles.Viewer }}></Story>

<Story name="Moderator" args={{ role: RoomUserRoles.Moderator }}></Story>

<Story name="Administrator" args={{ role: RoomUserRoles.Administrator }}></Story>

<Story name="Creator" args={{ role: RoomUserRoles.Creator }}></Story>

<Story name="x4" args={{ role: RoomUserRoles.Administrator, size: 16 }}></Story>
