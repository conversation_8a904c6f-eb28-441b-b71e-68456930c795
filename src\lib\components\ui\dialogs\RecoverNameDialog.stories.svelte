<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import RecoverNameDialog from "./RecoverNameDialog.svelte";
	import { setAppUserState } from "$lib/states/appUserState.svelte";
	import { AppUserService } from "$lib/services/appUserService";

	const { Story } = defineMeta({
		title: "Dialogs/RecoverNameDialog",
		component: RecoverNameDialog,
		argTypes: {
			isOpen: {
				control: "boolean",
				defaultValue: false
			}
		}
	});
</script>

<script lang="ts">
	const appUserState = setAppUserState();
	const appUserService = AppUserService.setInstance(appUserState);

	appUserService.setCurrentUser({
		id: 1,
		username: "User1",
		email: "<EMAIL>",
		token: "testToken"
	});
</script>

<Story name="Primary" args={{ isOpen: true }} />
