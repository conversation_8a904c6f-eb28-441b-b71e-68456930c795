<script lang="ts">
	import type { SettingsMenuProps } from "$lib/models/props/settingsMenuProps";
	import AudioSubmenu from "./AudioSubmenu.svelte";
	import CaptionSubmenu from "./CaptionSubmenu.svelte";
	import Menu from "./Menu.svelte";

	let {
		isSubtitleMenuVisible,
		isSubtitleShiftMenuVisible,
		placement,
		tooltipPlacement,
		onSetSubtitleOffsetByUser
	}: SettingsMenuProps = $props();
</script>

<Menu {placement} {tooltipPlacement}>
	{#snippet button()}
		<media-icon
			class="h-8 w-8 transform transition-transform duration-200 ease-out group-data-[open]:rotate-90"
			type="settings"
		></media-icon>
	{/snippet}

	{#snippet content()}
		{#if isSubtitleMenuVisible}
			<CaptionSubmenu {isSubtitleShiftMenuVisible} {onSetSubtitleOffsetByUser}></CaptionSubmenu>
		{/if}
		<AudioSubmenu></AudioSubmenu>
	{/snippet}

	{#snippet tooltipContent()}
		Settings
	{/snippet}
</Menu>
