<script lang="ts">
	import type { AudioGainSubmenuProps } from "$lib/models/props/audioGainSubmenuProps";
	import { onMount } from "svelte";

	let { hint = $bindable() }: AudioGainSubmenuProps = $props();
	let sliderEl: any;

	onMount(() => {
		sliderEl.subscribe(({ value }: { value: number }) => {
			hint = `${Math.round(value)}%`;
		});
	});

	hint = "OFFFF";
</script>

<div class="flex items-center px-3 py-0 bg-player-neutral-900 rounded-lg mt-2">
	<media-icon class="h-6 w-6 text-foreground" type="volume-low"></media-icon>
	<media-audio-gain-slider
		min={0}
		step={1}
		max={300}
		keyStep={25}
		bind:this={sliderEl}
		class="group relative mx-[7.5px] inline-flex h-10 w-full cursor-pointer touch-none select-none items-center outline-none aria-hidden:hidden"
	>
		<!-- Track -->
		<div
			class="relative z-0 h-1.5 w-full rounded-sm bg-player-neutral-800 ring-foreground/40 group-data-[focus]:ring-[3px]"
		>
			<!-- Track Fill -->
			<div
				class="absolute h-full w-[var(--slider-fill)] rounded-sm bg-foreground will-change-[width]"
			></div>
		</div>
		<!-- Thumb -->
		<div
			class="absolute left-[var(--slider-fill)] top-1/2 z-20 h-[15px] w-[15px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-player-neutral-100 bg-foreground ring-foreground/40 transition-opacity will-change-[left] group-data-[dragging]:ring-4"
		></div>
	</media-audio-gain-slider>
	<media-icon class="h-6 w-6 text-foreground" type="volume-high"></media-icon>
</div>
