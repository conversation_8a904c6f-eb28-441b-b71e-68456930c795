import type { RoomUser } from "$lib/models/roomUser";
import { getContext, setContext } from "svelte";

export class RoomUsersState {
	users: RoomUser[] = $state([]);

	public constructor() {}

	set(users: RoomUser[]): void {
		this.users = sortUsers(users);
	}

	add(user: RoomUser): void {
		this.users.push(user);
		this.users = sortUsers(this.users);
	}

	remove(user: RoomUser): void {
		this.removeById(user.id);
	}

	removeById(id: number): void {
		this.users = this.users.filter((u) => u.id !== id);
	}

	update(user: RoomUser): void {
		const existingUserIndex = this.users.findIndex((u) => u.id === user.id);
		if (existingUserIndex < 0) {
			return;
		}

		this.users[existingUserIndex] = user;
		this.users = sortUsers(this.users);
	}

	clear(): void {
		this.users = [];
	}
}

const ROOM_USERS_KEY = Symbol("WATCH_APP_ROOM_USERS");

export function setRoomUsersState(): RoomUsersState {
	return setContext(ROOM_USERS_KEY, new RoomUsersState());
}

export function getRoomUsersState(): ReturnType<typeof setRoomUsersState> {
	return getContext<ReturnType<typeof setRoomUsersState>>(ROOM_USERS_KEY);
}

function sortUsers(users: RoomUser[]): RoomUser[] {
	return users.sort((a, b) => -(a.role - b.role) || a.username.localeCompare(b.username));
}
