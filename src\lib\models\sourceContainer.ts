export interface SourceContainer {
	id: number;
	type: string;
	video?: VideoSource;
	subtitles?: Array<SubtitleSource>;
	attributes?: Record<string, string>;
	externalInfo?: BaseExternalInfo;
}

export interface VideoSource {
	url: string;
	attributes?: Record<string, string>;
	width?: number;
	height?: number;
}

export interface SubtitleSource {
	url: string;
	label: string;
	srcLang: string;
	offset: number;
	attributes?: Record<string, string>;
}

export interface BaseExternalInfo {
	title: string;
	type: string;
}

export interface BaseMovieAndSeriesInfo extends BaseExternalInfo {
	type: "movie" | "series";
	originalTitle?: string;
	description?: string;
	releaseDate?: string;
	posterImageUrl?: string;
	backdropImageUrl?: string;
	backdropPlaceholderUrl?: string;
	imdbId?: string;
	originCountries?: Array<string>;
	genres?: Array<string>;
	spokenLanguages?: Array<string>;
	networks?: Array<Company>;
	productionCompanies?: Array<Company>;
	credits?: BaseMoviesAndSeriesCredits;
}

export interface BaseMoviesAndSeriesCredits {
	actors?: Array<Actor>;
	writers?: Array<Crew>;
	directors?: Array<Crew>;
	originalMusicComposers?: Array<Crew>;
}

export interface MovieInfo extends BaseMovieAndSeriesInfo {
	tagline?: string;
	budget?: number;
	credits?: MovieCredits;
}

export interface SeriesInfo extends BaseMovieAndSeriesInfo {
	seasonNumber?: number;
	episodeNumber?: number;
	episodeTitle?: string;
	credits?: SeriesCredits;
}

export interface MovieCredits extends BaseMoviesAndSeriesCredits {}

export interface SeriesCredits extends BaseMoviesAndSeriesCredits {
	guestActors?: Array<Actor>;
	creators?: Array<Crew>;
}

export interface Company {
	name?: string;
	logoImageUrl?: string;
}

export interface Crew {
	name?: string;
	profileImageUrl?: string;
}

export interface Actor extends Crew {
	character?: string;
	order?: number;
}

export function isMovieOrSeriesInfo(
	externalInfo: BaseExternalInfo | undefined
): externalInfo is BaseMovieAndSeriesInfo {
	if (!externalInfo) {
		return false;
	}

	return externalInfo.type === "movie" || externalInfo.type === "series";
}

export function isMovieInfo(externalInfo: BaseExternalInfo | undefined): externalInfo is MovieInfo {
	if (!externalInfo) {
		return false;
	}

	return externalInfo.type === "movie";
}

export function isSeriesInfo(
	externalInfo: BaseExternalInfo | undefined
): externalInfo is SeriesInfo {
	if (!externalInfo) {
		return false;
	}

	return externalInfo.type === "series";
}
