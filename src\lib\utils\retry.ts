import { CancelledError } from "$lib/models/result/cancelledError";
import { fail, type Result, type ValueResult } from "$lib/models/result/result";
import type { ResultError } from "$lib/models/result/resultError";
import { sleep } from "./sleep";

const DEFAULT_MAX_RETRIES = 3;

// Retry a function until it returns a successful result
// or the maximum number of retries is reached
export async function retryResult(
	func: () => Promise<Result>,
	retries: number,
	retryDelayMs: number,
	onFailure?: (error: ResultError) => Promise<void>,
	cancellationToken?: CancellationToken
): Promise<Result>;
export async function retryResult<TValue>(
	func: () => Promise<ValueResult<TValue>>,
	retries: number,
	retryDelayMs: number,
	onFailure?: (error: ResultError) => Promise<void>,
	cancellationToken?: CancellationToken
): Promise<ValueResult<TValue>>;
export async function retryResult<TValue>(
	func: (() => Promise<Result>) | (() => Promise<ValueResult<TValue>>),
	retries = DEFAULT_MAX_RETRIES,
	retryDelayMs: number = 0,
	onFailure?: (error: ResultError) => Promise<void>,
	cancellationToken?: CancellationToken
): Promise<Result | ValueResult<TValue>> {
	if (retries < 0) {
		throw new Error("Number of retries must be greater than or equal to 0");
	}

	let lastResult: Result | ValueResult<TValue>;
	let retriesLeft = retries;

	const retryFunc = async () => {
		if (cancellationToken?.isCancelled) {
			return fail(new CancelledError("Retrying the execultion has been cancelled"));
		}

		if (retriesLeft < 0) {
			return lastResult;
		}

		const result = await func();
		if (result.isSuccess) {
			return result;
		}

		if (cancellationToken?.isCancelled) {
			return fail(new CancelledError("Retrying the execultion has been cancelled"));
		}

		await onFailure?.(result.error);

		lastResult = result;
		retriesLeft -= 1;

		if (retryDelayMs > 0) {
			await sleep(retryDelayMs);
		}

		return retryFunc();
	};

	return retryFunc();
}

export class CancellationToken {
	private _isCancelled = false;

	get isCancelled() {
		return this._isCancelled;
	}

	cancel() {
		this._isCancelled = true;
	}
}
