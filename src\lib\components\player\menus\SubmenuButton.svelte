<script lang="ts">
	import type { SubmenuButtonProps } from "$lib/models/props/submenuButtonProps";
	import ChevronLeftIcon from "~icons/media-icons/chevron-left";
	import ChevronRightIcon from "~icons/media-icons/chevron-right";

	let { label, hint, icon }: SubmenuButtonProps = $props();
</script>

<media-menu-button
	class="ring-media-focus parent left-0 z-10 flex w-full cursor-pointer select-none items-center justify-start rounded-lg bg-player-neutral-950 p-2.5 outline-none ring-inset data-[open]:sticky data-[open]:-top-2.5 data-[hocus]:bg-[#1c1b1b] data-[focus]:ring-[3px] aria-hidden:hidden aria-disabled:hidden"
>
	<ChevronLeftIcon class="parent-data-[open]:block -ml-0.5 mr-1.5 hidden h-[18px] w-[18px] shrink-0"
	></ChevronLeftIcon>
	<div class="contents parent-data-[open]:hidden">
		{@render icon()}
	</div>
	<span class="ml-1.5 parent-data-[open]:ml-0">{label}</span>
	<div class="flex-1"></div>
	<span class="truncate ml-2 text-sm text-foreground/50" data-part="hint">{hint ? hint : ""}</span>
	<ChevronRightIcon
		class="parent-data-[open]:hidden ml-0.5 h-[18px] w-[18px] text-sm text-foreground/50 shrink-0"
	></ChevronRightIcon>
</media-menu-button>
