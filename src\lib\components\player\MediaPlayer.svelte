<script lang="ts">
	import "vidstack/player/styles/base.css";
	import "vidstack/player";
	import "vidstack/player/ui";
	import "vidstack/icons";
	import type { MediaPlayerProps } from "$lib/models/props/videoPlayerProps";
	import type { MediaPlayerElement } from "vidstack/elements";
	import { IntervalRunner } from "$lib/utils/intervalRunner";
	import type { SynchronizationContext } from "$lib/models/synchronizationContext";
	import {
		isMovieOrSeriesInfo,
		isSeriesInfo,
		type SourceContainer
	} from "$lib/models/sourceContainer";
	import { PlaybackState } from "$lib/models/playbackInfo";
	import { onDestroy, onMount, untrack } from "svelte";
	import VideoLayout from "./layouts/VideoLayout.svelte";
	import { TextTrack } from "vidstack";
	import type { PlayerSrc, VideoMimeType } from "vidstack";
	import Poster from "./Poster.svelte";
	import { setSelectedSubtitleState } from "$lib/states/selectedSubtitleState.svelte";
	import { displayError, displayWarning } from "$lib/utils/toastUtils";
	import { PersistentStorage } from "$lib/storage/persistentStorage.svelte";
	import { formatEpisodeNumber } from "$lib/utils/formatEpisodeNumber";

	const defaultVolume = 1;
	const defaultMuted = false;
	const synchronizationAbsoluteRate = 2;
	const synchronizationSpeedUpRate = synchronizationAbsoluteRate;
	const synchronizationSlowDownRate = 1 / synchronizationAbsoluteRate;
	const synchronizationIntervalMs = 1000 / 30;
	const videoMimeTypes = [
		"video/mp4",
		"video/webm",
		"video/3gp",
		"video/ogg",
		"video/avi",
		"video/mpeg",
		"video/object"
	];

	const isMutedStorage = new PersistentStorage<boolean>("isMuted", defaultMuted);
	const playerVolumeStorage = new PersistentStorage<number>("playerVolume", defaultVolume);
	const isUsingSubtitlesStorage = new PersistentStorage<boolean>("isUsingSubtitles", false);
	const lastSubtitlesLanguageStorage = new PersistentStorage<string>(
		"lastSubtitlesLanguage",
		undefined
	);
	const lastSubtitlesNameStorage = new PersistentStorage<string>("lastSubtitlesName", undefined);

	let {
		source,
		isTheatreMode,
		showMutedPlaybackBanner,
		showSubtitleShiftMenu,
		onToggleTheatreMode,
		onPlay,
		onPlaying,
		onSeeking,
		onSeeked,
		onEnded,
		onPause,
		onStalled,
		onSuspended,
		onWaiting,
		onPlayByUser,
		onPauseByUser,
		onSeekingByUser,
		onSetSubtitleOffsetByUser
	}: MediaPlayerProps = $props();

	let player: MediaPlayerElement;
	let videoLayout: VideoLayout;
	let currentSynchronizationRunner: IntervalRunner<SynchronizationContext> | undefined;
	let disposeStartedSubscription: () => void | undefined;
	let disposeCanPlaySubscription: () => void | undefined;
	let disposeMutedSubscription: () => void | undefined;
	let disposePlayingSubscription: () => void | undefined;
	let disposeTrackSubscription: () => void | undefined;
	let disposeTextTracksSubscription: () => void | undefined;
	let disposeVolumeSubscription: () => void | undefined;
	let disposeErrorSubscription: () => void | undefined;
	let isPosterVisible = $state(true);
	let isMutedBannerVisible = $state(false);
	let isAutomuted = $state(false);
	let canPlayerPlay = false;
	let hasPlayedBefore = false;
	let isInitialTextTrackEmitted = false;
	let subtitleSrcToOffsetMap: Map<string, number> = new Map();
	let selectedSubtitleState = setSelectedSubtitleState();
	let initialIsMuted: boolean = isMutedStorage.value ?? defaultMuted;
	let initialVolume: number = playerVolumeStorage.value ?? defaultVolume;
	let isSubtitleMenuVisible = $state(false);
	let errorMessage = $state("");
	let playerSrc: PlayerSrc = $state("");
	let backdropUrl: string | undefined = $derived.by(() => {
		if (!source) {
			return "";
		}

		if (source.externalInfo && isMovieOrSeriesInfo(source.externalInfo)) {
			return source.externalInfo.backdropImageUrl;
		}
	});
	let title: string | undefined = $derived.by(() => {
		if (!source) {
			return "";
		}

		if (source.externalInfo && isMovieOrSeriesInfo(source.externalInfo)) {
			return source.externalInfo.title;
		}
	});
	let artist: string | undefined = $derived.by(() => {
		if (!source) {
			return "";
		}

		if (source.externalInfo && isSeriesInfo(source.externalInfo)) {
			return formatEpisodeNumber(
				source.externalInfo.episodeNumber,
				source.externalInfo.seasonNumber
			);
		}

		return "";
	});

	onMount(() => {
		// Hack to set the initial player volume & muted status
		// Issues with initial volume/muted setting appear after vidstack 1.11.30-next
		waitUntilCanPlay().then(() => {
			setTimeout(() => {
				player.volume = initialVolume;
				player.muted = initialIsMuted;
			}, 0);
		});

		hookPlayerEvents();

		disposeStartedSubscription = player.subscribe(({ started }) => {
			isPosterVisible = !started;
		});

		disposeCanPlaySubscription = player.subscribe(({ canPlay }) => {
			canPlayerPlay = canPlay;
		});

		disposeMutedSubscription = player.subscribe(({ muted }) => {
			// Hide banner when unmuting with icon
			if (!muted && isMutedBannerVisible) {
				isMutedBannerVisible = false;
			}

			// Unset automuted when unmuting with icon
			if (!muted && isAutomuted) {
				isAutomuted = false;
			}

			if (muted && !isAutomuted) {
				isMutedStorage.value = muted;
			} else if (!muted) {
				isMutedStorage.value = muted;
			}
		});

		disposePlayingSubscription = player.subscribe(({ playing }) => {
			if (playing) {
				hasPlayedBefore = true;
			}
		});

		disposeTrackSubscription = player.subscribe(({ textTrack }) => {
			if (!textTrack) {
				selectedSubtitleState.src = undefined;
				selectedSubtitleState.offset = undefined;

				// Ignore initial empty text track auto selection
				if (!isInitialTextTrackEmitted) {
					isInitialTextTrackEmitted = true;
					return;
				}

				// User unselected subtitles, save it
				if (player.textTracks.length > 0) {
					persistSubtitlesState(textTrack);
				}

				return;
			}

			if (!textTrack.src) {
				console.error("textTrack.src is undefined");
				return;
			}

			const offset = subtitleSrcToOffsetMap.get(textTrack.src);
			selectedSubtitleState.offset = offset;
			selectedSubtitleState.src = textTrack.src;

			persistSubtitlesState(textTrack);
		});

		disposeTextTracksSubscription = player.subscribe(({ textTracks }) => {
			if (textTracks.length > 0) {
				isSubtitleMenuVisible = true;
			} else {
				isSubtitleMenuVisible = false;
				return;
			}

			// Reset subtitle src to offset map
			subtitleSrcToOffsetMap.clear();
			source?.subtitles?.forEach((subtitle) => {
				subtitleSrcToOffsetMap.set(subtitle.url, subtitle.offset);
			});

			// Hook cues for all tracks
			textTracks.forEach((textTrack) => {
				textTrack.addEventListener("load", (e) => {
					if (!e.target.src) {
						return;
					}

					const offset = subtitleSrcToOffsetMap.get(e.target.src);
					e.target.cues.forEach((cue) => {
						hookCue(cue, offset);
					});
				});
			});

			// Select subtitles depending on stored values
			if (isUsingSubtitlesStorage.value) {
				let matchingLanguageSubtitles: TextTrack | undefined;
				let matchingNameSubtitles: TextTrack | undefined;
				const firstSubtitles = textTracks[0];
				for (let i = 0; i < textTracks.length; i++) {
					const subtitles = textTracks[i];
					if (subtitles.label === lastSubtitlesNameStorage.value) {
						matchingNameSubtitles = subtitles;
						break;
					} else if (
						!matchingLanguageSubtitles &&
						subtitles.language === lastSubtitlesLanguageStorage.value
					) {
						matchingLanguageSubtitles = subtitles;
					}
				}
				if (matchingNameSubtitles) {
					matchingNameSubtitles.mode = "showing";
				} else if (matchingLanguageSubtitles) {
					matchingLanguageSubtitles.mode = "showing";
				} else {
					firstSubtitles.mode = "showing";
				}
			}
		});

		disposeVolumeSubscription = player.subscribe(({ volume }) => {
			playerVolumeStorage.value = volume;
		});

		disposeErrorSubscription = player.subscribe(({ error }) => {
			if (error) {
				errorMessage = error.message;
			}
		});
	});

	onDestroy(() => {
		disposeStartedSubscription?.();
		disposeCanPlaySubscription?.();
		disposeMutedSubscription?.();
		disposePlayingSubscription?.();
		disposeTrackSubscription?.();
		disposeTextTracksSubscription?.();
		disposeVolumeSubscription?.();
		disposeErrorSubscription?.();
		player?.destroy();

		cancelCurrentSynchronizationRunner();
	});

	// Handle selected subtitle state updates
	$effect(() => {
		if (selectedSubtitleState.offset === undefined || selectedSubtitleState.src === undefined) {
			return;
		}

		if (subtitleSrcToOffsetMap.has(selectedSubtitleState.src)) {
			subtitleSrcToOffsetMap.set(selectedSubtitleState.src, selectedSubtitleState.offset);
		} else {
			displayError("Interal Error", "Subtitle not found in subtitleSrcToOffsetMap");
		}

		const selectedTextTrack = player.textTracks.selected;
		if (!selectedTextTrack) {
			displayWarning("Internal error", "No selected text track");
			return;
		}

		selectedTextTrack.cues.forEach((cue) => {
			// @ts-ignore
			cue.offset = subtitleSrcToOffsetMap.get(selectedTextTrack.src || "") ?? 0;
		});
	});

	$effect(() => {
		source;
		untrack(() => {
			if (!source) {
				playerSrc = "";
				return;
			}

			if (source.type === "mp4" && source.video) {
				try {
					validateVideoSource(source);
				} catch (error) {
					if (error instanceof Error) {
						displayError("Failed to validate video source", error);
					} else {
						displayError("Failed to validate video source");
					}
					playerSrc = "";
					return;
				}

				playerSrc = {
					src: source.video.url,
					type: source.video?.attributes?.["type"] as VideoMimeType
				};
				return;
			}

			displayError("Unrecognized video source");
			playerSrc = "";
			return;
		});
	});

	export function play() {
		if (showMutedPlaybackBanner && !hasPlayedBefore) {
			return player.play().catch(() => {
				if (player.muted) {
					displayError(
						"Playback failed",
						"Failed to start video playback due to an unknown error (1)"
					);
					return;
				}

				isAutomuted = true;
				mute();
				// Subscribe for muted to make sure that playback is started only once video is muted
				const disposeMutedLocalSubscription = player.subscribe(({ muted }) => {
					if (!muted) {
						return;
					}

					disposeMutedLocalSubscription();
					player
						.play()
						.then(() => {
							isMutedBannerVisible = true;
						})
						.catch(() => {
							displayError(
								"Playback failed",
								"Failed to start video playback due to an unknown error (2)"
							);

							isAutomuted = false;
							unmute();
						});
				});
			});
		} else {
			return player.play();
		}
	}

	export function pause() {
		return player.pause().then(() => {
			// Fix paused state because on Brave broswer, the player
			// tends to be stuck in a paused state forever after
			// player is paused by another user.
			player.$state.paused.set(true);
			player.$state.playing.set(false);
		});
	}

	// Need to use a bit hacky workaround for resetting the current time
	// This is needed because the vidstack player's state can break when setting
	// current time right after pausing.
	// To reproduce, client 1 plays a video, then sets a new video source.
	// This should pause client 2 video, but the player is stuck in a playing state forever.
	export function stop() {
		return player.pause().finally(() => {
			setTimeout(() => {
				player.currentTime = 0;
			}, 0);
		});
	}

	export function getPlaybackState(): PlaybackState {
		if (player.paused) {
			if (player.currentTime > 0) {
				return PlaybackState.Paused;
			} else {
				return PlaybackState.Stopped;
			}
		}

		return PlaybackState.Playing;
	}

	export function getCurrentTime(): number {
		return player.currentTime;
	}

	export function setCurrentTime(seconds: number): void {
		player.currentTime = seconds;
	}

	export function getCurrentVolume(): number {
		return player.volume;
	}

	export function setCurrentVolume(volume: number): void {
		player.volume = volume;
	}

	export function getCanPlay(): boolean {
		return canPlayerPlay;
	}

	export function mute(): void {
		player.muted = true;
	}

	export function unmute(): void {
		player.muted = false;
	}

	export function isMuted(): boolean {
		return player.muted;
	}

	export function waitUntilCanPlay(): Promise<void> {
		return new Promise((resolve) => {
			const disposeCanPlaySubscription = player.subscribe(({ canPlay }) => {
				if (canPlay) {
					disposeCanPlaySubscription();
					resolve();
				}
			});
		});
	}

	export function waitUntilMuted(): Promise<void> {
		return new Promise((resolve) => {
			const disposeMutedSubscription = player.subscribe(({ muted }) => {
				if (muted) {
					disposeMutedSubscription();
					resolve();
				}
			});
		});
	}

	export function waitUntilUnmuted(): Promise<void> {
		return new Promise((resolve) => {
			const disposeMutedSubscription = player.subscribe(({ muted }) => {
				if (muted) {
					disposeMutedSubscription();
					resolve();
				}
			});
		});
	}

	export function synchronizePlayback(
		synchronizationDeltaSecs: number,
		isFluidSynchronization?: boolean
	): void {
		if (!isFluidSynchronization) {
			const targetTime = getCurrentTime() + synchronizationDeltaSecs;
			setCurrentTime(targetTime);

			return;
		}

		const synchronizationDeltaMs = synchronizationDeltaSecs * 1000;
		const absoluteSynchronizationDeltaMs = Math.abs(synchronizationDeltaMs);
		let synchronizationDurationMs =
			absoluteSynchronizationDeltaMs / (synchronizationAbsoluteRate - 1);
		let synchronizationRate = synchronizationSpeedUpRate;
		if (synchronizationDeltaMs < 0) {
			synchronizationDurationMs += absoluteSynchronizationDeltaMs;
			synchronizationRate = synchronizationSlowDownRate;
		}

		const synchronizationContext: SynchronizationContext = {
			timeDifferenceSecs: synchronizationDeltaSecs,
			synchronizationDurationMs: synchronizationDurationMs,
			synchronizationRate: synchronizationRate,
			initialTimestamp: 0
		};

		currentSynchronizationRunner = new IntervalRunner<SynchronizationContext>({
			context: synchronizationContext,
			intervalMs: synchronizationIntervalMs,
			onBeforeStart: (context: SynchronizationContext, timestamp: DOMHighResTimeStamp) => {
				context.initialTimestamp = timestamp;
				player.playbackRate = context.synchronizationRate;
			},
			onElapsed: (context: SynchronizationContext, timestamp: DOMHighResTimeStamp) => {
				const timestampDifference = timestamp - context.initialTimestamp;
				if (timestampDifference >= context.synchronizationDurationMs) {
					context.isCompletionRequested = true;
				}
			},
			onCompleted: (context: SynchronizationContext) => {
				player.playbackRate = 1;
			}
		});

		currentSynchronizationRunner.start();
	}

	export function addSeekedSeconds(seconds: number): void {
		videoLayout.addSeekedSeconds(seconds);
	}

	export function resetSeekedSeconds(): void {
		videoLayout.resetSeekedSeconds();
	}

	export function setSubtitleOffset(src: string, offset: number): void {
		if (subtitleSrcToOffsetMap.has(src)) {
			subtitleSrcToOffsetMap.set(src, offset);
		} else {
			displayError("Internal error", "Subtitle not found in subtitleSrcToOffsetMap");
		}

		if (selectedSubtitleState.src === src) {
			selectedSubtitleState.offset = offset;
		}
	}

	export function getPartialStateSnapshot(names: string[]): Record<string, any> {
		// @ts-ignore
		return Object.fromEntries(names.map((name) => [name, player.$state[name]()]));
	}

	export function getFullStateSnapshot(): Record<string, any> {
		const snapshot: Record<string, any> = {};
		for (const prop in player.$state) {
			// @ts-ignore
			if (typeof player.$state[prop].set !== "function") {
				continue;
			}

			// @ts-ignore
			snapshot[prop] = player.$state[prop]();
		}

		return snapshot;
	}

	export function restoreStateSnapshot(snapshot: Record<string, any>) {
		for (const [name, value] of Object.entries(snapshot)) {
			// @ts-ignore
			player.$state[name].set(value);
		}
	}

	export function resetSubtitles(): void {
		// Clear existing subtitle mappings
		subtitleSrcToOffsetMap.clear();

		// Reset selected subtitle state
		selectedSubtitleState.src = undefined;
		selectedSubtitleState.offset = undefined;

		// Clear existing text tracks
		player.textTracks.clear();

		// Reset subtitle menu visibility
		isSubtitleMenuVisible = false;

		// Reset initial text track emitted flag
		isInitialTextTrackEmitted = false;
	}

	function cancelCurrentSynchronizationRunner(): void {
		if (currentSynchronizationRunner) {
			currentSynchronizationRunner.cancel();
			currentSynchronizationRunner = undefined;
		}
	}

	function persistSubtitlesState(subtitles?: TextTrack | undefined | null): void {
		if (!subtitles) {
			isUsingSubtitlesStorage.value = false;
			lastSubtitlesLanguageStorage.value = undefined;
			lastSubtitlesNameStorage.value = undefined;

			return;
		}

		isUsingSubtitlesStorage.value = true;
		lastSubtitlesLanguageStorage.value = subtitles.language;
		lastSubtitlesNameStorage.value = subtitles.label;
	}

	function validateVideoSource(source: SourceContainer) {
		if (!source.video) {
			throw new TypeError("source.video must not be undefined");
		}

		if (!source.video.attributes) {
			throw new TypeError(
				"source.video.attributes must not be undefined when source.type is 'mp4'"
			);
		}

		if (!("type" in source.video.attributes)) {
			throw new TypeError("source.video.attributes must contain 'type' attribute");
		}

		const videoMimeType = source.video.attributes["type"].split(";")[0];
		if (!videoMimeTypes.includes(videoMimeType)) {
			throw new TypeError(
				`source.video.attributes["type"] contains an unsupported mime type - "${source.video.attributes["type"]}`
			);
		}
	}

	function hookPlayerEvents() {
		player.addEventListener("play", (e) => {
			onPlay?.();
		});

		player.addEventListener("playing", (e) => {
			isPosterVisible = false;

			onPlaying?.();
		});

		player.addEventListener("seeking", (event) => {
			onSeeking?.();
		});

		player.addEventListener("seeked", () => {
			if (player.currentTime > 0) {
				isPosterVisible = false;
			} else {
				isPosterVisible = true;
			}

			onSeeked?.();
		});

		player.addEventListener("ended", () => {
			cancelCurrentSynchronizationRunner();
			onEnded?.();
		});

		player.addEventListener("pause", (e) => {
			cancelCurrentSynchronizationRunner();
			onPause?.();
		});

		player.addEventListener("stalled", (e) => {
			cancelCurrentSynchronizationRunner();
			onStalled?.();
		});

		player.addEventListener("suspend", (e) => {
			cancelCurrentSynchronizationRunner();
			onSuspended?.();
		});

		player.addEventListener("waiting", () => {
			cancelCurrentSynchronizationRunner();
			onWaiting?.();
		});

		player.addEventListener("media-play-request", (event) => {
			onPlayByUser?.(event);
		});

		player.addEventListener("media-pause-request", (_) => {
			onPauseByUser?.();
		});

		player.addEventListener(
			"media-seek-request",
			(event) => {
				const keydownTrigger = event.triggers.findType("keydown") as KeyboardEvent;
				if (keydownTrigger && keydownTrigger.code.startsWith("Digit")) {
					event.preventDefault();
					event.stopImmediatePropagation();

					return;
				}

				const seconds = event.detail ?? player.currentTime;
				let keyboardSeekDeltaSeconds = 0;
				if (event.triggers.hasType("keyup")) {
					keyboardSeekDeltaSeconds = Math.round(seconds - player.currentTime);
				}

				if (keyboardSeekDeltaSeconds === 0) {
					resetSeekedSeconds();
				} else {
					addSeekedSeconds(keyboardSeekDeltaSeconds);
				}

				onSeekingByUser?.(seconds, keyboardSeekDeltaSeconds);
			},
			true
		);
	}

	function hookCue(cue: any, offset: number = 0): void {
		if (Object.hasOwn(cue, "offset")) {
			return;
		}

		Object.defineProperty(cue, "offset", { value: offset, writable: true });
		Object.defineProperty(cue, "baseStartTime", { value: cue.startTime, writable: true });
		Object.defineProperty(cue, "baseEndTime", { value: cue.endTime, writable: true });
		Object.defineProperty(cue, "startTime", {
			get: function () {
				return this.baseStartTime + this.offset;
			},
			set: function (value) {
				this.baseStartTime = value;
			}
		});
		Object.defineProperty(cue, "endTime", {
			get: function () {
				return this.baseEndTime + this.offset;
			},
			set: function (value) {
				this.baseEndTime = value;
			}
		});
	}

	function onClickMutedBannerUnmute() {
		isAutomuted = false;
		unmute();
		isMutedBannerVisible = false;
	}
</script>

<media-player
	class="w-full h-full ring-media-focus data-[focus]:ring-4 aspect-auto"
	playsInline
	bind:this={player}
	src={playerSrc}
	poster={backdropUrl}
	{title}
	{artist}
	crossOrigin={true}
	loop={false}
	preload="auto"
	load="eager"
	keyShortcuts={{
		togglePaused: "k Space",
		toggleMuted: "m",
		toggleFullscreen: "f",
		volumeUp: "ArrowUp",
		volumeDown: "ArrowDown",
		seekBackward: "j ArrowLeft",
		seekForward: "l ArrowRight"
	}}
	keyTarget="document"
>
	<media-provider
		class={["transition-opacity duration-500 ease-in-out bg-black", isPosterVisible && "opacity-0"]}
	>
		{#if source && source.subtitles}
			{#each source.subtitles as subtitle}
				<track
					src={subtitle.url ?? ""}
					label={subtitle.label}
					srclang={subtitle.srcLang}
					kind="captions"
				/>
			{/each}
		{/if}
	</media-provider>

	<Poster
		src={backdropUrl}
		alt={source?.externalInfo?.title || ""}
		crossOrigin={true}
		isVisible={isPosterVisible}
	></Poster>

	<VideoLayout
		bind:this={videoLayout}
		{isTheatreMode}
		{isMutedBannerVisible}
		{isSubtitleMenuVisible}
		isSubtitleShiftMenuVisible={showSubtitleShiftMenu}
		{errorMessage}
		{onToggleTheatreMode}
		{onClickMutedBannerUnmute}
		{onSetSubtitleOffsetByUser}
	></VideoLayout>
</media-player>

<style>
	media-player {
		display: flex !important;
	}
</style>
