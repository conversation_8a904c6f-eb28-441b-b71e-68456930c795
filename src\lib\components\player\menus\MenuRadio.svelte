<script lang="ts">
	import RadioButtonIcon from "~icons/media-icons/radio-button";
	import RadioButtonSelectedIcon from "~icons/media-icons/radio-button-selected";
</script>

<media-radio
	class="ring-media-focus group relative flex w-full cursor-pointer select-none items-center justify-start rounded-lg p-2.5 outline-none data-[hocus]:bg-foreground/10 data-[focus]:ring-[3px]"
>
	<RadioButtonIcon class="h-4 w-4 shrink-0 text-foreground group-data-[checked]:hidden"
	></RadioButtonIcon>
	<RadioButtonSelectedIcon
		class="text-media-brand hidden h-4 w-4 shrink-0 group-data-[checked]:block"
	></RadioButtonSelectedIcon>
	<span class="ml-2 truncate" data-part="label"></span>
</media-radio>
