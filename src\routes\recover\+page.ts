import { ApiService } from "$lib/services/apiService";
import { error } from "@sveltejs/kit";
import type { PageLoadEvent } from "./$types";

export async function load(event: PageLoadEvent) {
	const fetch = event.fetch;
	const id = Number(event.url.searchParams.get("id"));
	const token = event.url.searchParams.get("token");
	const apiService = new ApiService(fetch);

	if (isNaN(id)) {
		throw error(422, "Provided id is invalid");
	}

	if (!token) {
		throw error(422, "Recovery token not provided");
	}

	const recoveryResponse = await apiService.verifyRecoveryToken(id, token);
	if (!recoveryResponse.ok || recoveryResponse.body === undefined) {
		return {
			error: recoveryResponse.error?.message ?? "Failed to verify recovery token"
		};
	}

	let user = recoveryResponse.body;

	return {
		user
	};
}
