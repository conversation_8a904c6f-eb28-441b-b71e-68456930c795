<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import VerifyPage from "./+page.svelte";
	import RootLayout from "../+layout.svelte";

	const { Story } = defineMeta({
		title: "Pages/Verify",
		component: VerifyPage,
		// @ts-ignore
		decorators: [() => RootLayout],
		parameters: { layout: "fullscreen" }
	});
</script>

<Story
	name="Primary"
	args={{
		data: {
			user: {
				id: 1,
				username: "User1",
				token: "testToken",
				email: "<EMAIL>",
				emailConfirmed: true
			},
			isUsernameSet: false,
			usernameError: ""
		}
	}}
></Story>

<Story
	name="WithSetUsername"
	args={{
		data: {
			user: {
				id: 1,
				username: "User1",
				token: "testToken",
				email: "<EMAIL>",
				emailConfirmed: true
			},
			isUsernameSet: true,
			usernameError: ""
		}
	}}
></Story>

<Story
	name="WithUsernameError"
	args={{
		data: {
			user: {
				id: 1,
				username: "User1",
				token: "testToken",
				email: "<EMAIL>",
				emailConfirmed: true
			},
			isUsernameSet: false,
			usernameError: "Name is already taken"
		}
	}}
></Story>

<Story
	name="WithAPIError"
	args={{
		data: {
			error: "Failed to verify confirmation token"
		}
	}}
></Story>
