<script lang="ts">
	import type { SetNameDialogProps } from "$lib/models/props/setNameDialogProps";
	import * as Dialog from "$lib/components/ui/dialog";
	import { Label } from "$lib/components/ui/label";
	import { Input } from "$lib/components/ui/input";
	import { Button } from "$lib/components/ui/button";
	import type { SetNameDialogStateType } from "$lib/types/setNameDialogStateType";
	import { CheckCircleIcon, MailCheckIcon } from "lucide-svelte";
	import Spinner from "$lib/components/ui/spinner/Spinner.svelte";
	import { getAppUserState, type AppUserState } from "$lib/states/appUserState.svelte";
	import type { AppUser } from "$lib/models/appUser";
	import { isEmailAddressValid } from "$lib/utils/isEmailAddressValid";
	import { isUsernameValidChars } from "$lib/utils/isUsernameValid";
	import { AppUserService } from "$lib/services/appUserService";
	import { displayError } from "$lib/utils/toastUtils";
	import { ChangeUsernameStatus } from "$lib/models/dto/responses/changeUsernameResponse";
	import { untrack } from "svelte";

	const MAX_USERNAME_LENGTH = 16;

	let { isOpen = $bindable() }: SetNameDialogProps = $props();

	const appUserState: AppUserState = getAppUserState();
	const appUserService: AppUserService = AppUserService.getInstance();

	let user: AppUser | undefined = $derived(appUserState.user);
	let dialogState: SetNameDialogStateType = $state("initial");
	let isCreateSpinnerVisible: boolean = $state(false);
	let username: string = $state("");
	let isUsernameValid: boolean = $state(false);
	let usernameError: string = $state("");
	let canDisplayUsernameErrors: boolean = $state(false);
	let email: string = $state("");
	let isEmailValid: boolean = $state(false);
	let emailError: string = $state("");
	let canDisplayEmailErrors: boolean = $state(false);

	$effect(() => {
		username;
		untrack(() => validateUsername(username));
	});

	$effect(() => {
		email;
		untrack(() => validateEmail(email));
	});

	$effect(() => {
		if (isOpen) {
			untrack(handleOpenDialog);
		} else {
			untrack(handleCloseDialog);
		}
	});

	function validateUsername(username: string) {
		const trimmedUsername = username.trim();
		if (trimmedUsername.length === 0) {
			usernameError = "Name is required";
			isUsernameValid = false;
			return;
		}

		if (trimmedUsername.length > MAX_USERNAME_LENGTH) {
			usernameError = "Name cannot exceed 16 characters";
			isUsernameValid = false;
			return;
		}

		if (!isUsernameValidChars(trimmedUsername)) {
			usernameError = "Name can only contain latin letters and digits";
			isUsernameValid = false;
			return;
		}

		usernameError = "";
		isUsernameValid = true;
	}

	function validateEmail(email: string) {
		const trimmedEmail = email.trim();
		if (trimmedEmail.length === 0) {
			emailError = "Email is required";
			isEmailValid = false;
			return;
		}

		if (!isEmailAddressValid(trimmedEmail)) {
			emailError = "Invalid email address";
			isEmailValid = false;
			return;
		}

		emailError = "";
		isEmailValid = true;
	}

	async function handleClickChangeName() {
		if (isCreateSpinnerVisible) {
			return;
		}

		if (!user) {
			return;
		}

		if (!isUsernameValid || !isEmailValid) {
			canDisplayUsernameErrors = true;
			canDisplayEmailErrors = true;
			return;
		}

		if (user.emailConfirmed && user.username === username) {
			dialogState = "success";
			return;
		}

		isCreateSpinnerVisible = true;
		const changeUsernameResult = await appUserService.changeUsername(username, email);
		isCreateSpinnerVisible = false;

		if (changeUsernameResult.isFailed) {
			displayError("Failed to change name", changeUsernameResult.error);
			return;
		}

		const changeUsernameResponse = changeUsernameResult.value;
		switch (changeUsernameResponse.status) {
			case ChangeUsernameStatus.Unchanged:
				dialogState = "success";
				break;
			case ChangeUsernameStatus.Changed:
				dialogState = "success";
				break;
			case ChangeUsernameStatus.EmailConfirmationRequired:
				dialogState = "confirmation_sent";
				break;
			case ChangeUsernameStatus.EmailAlreadyTaken:
				emailError = "Email address is already taken";
				isEmailValid = false;
				break;
			case ChangeUsernameStatus.UsernameAlreadyTaken:
				usernameError = "Username is already taken";
				isUsernameValid = false;
				break;
			default:
				displayError("Failed to change name");
				break;
		}
	}

	function handleClickClose() {
		isOpen = false;
	}

	function handleOpenDialog() {
		email = user?.email ?? "";
		username = user?.username ?? "";
		dialogState = "initial";
		canDisplayUsernameErrors = false;
		canDisplayEmailErrors = false;
	}

	function handleCloseDialog() {}

	function handleBlurUsername() {
		canDisplayUsernameErrors = true;
	}

	function handleBlurEmail() {
		canDisplayEmailErrors = true;
	}
</script>

<Dialog.Root bind:open={isOpen}>
	<Dialog.Content class="max-w-xs">
		<Dialog.Header>
			<Dialog.Title>Change name</Dialog.Title>
		</Dialog.Header>

		<div class="relative">
			<form
				class="flex flex-col space-y-2 transition-opacity duration-200 ease-out {dialogState !==
				'initial'
					? 'opacity-0'
					: 'opacity-100'}"
			>
				<div class="text-sm">
					<p>You can choose to keep your existing name or create a new one.</p>
					<p class="mt-1">
						Your email address will be used to register your name, allowing you to recover it later
						if needed.
					</p>
				</div>

				<div class="flex flex-col pt-2">
					<Label class="mb-2" for="username">Name</Label>
					<Input
						id="username"
						type="text"
						class="shadows-box-level1"
						placeholder="Enter desired name"
						required
						bind:value={username}
						onblur={handleBlurUsername}
					></Input>
					<span
						class="text-xs text-red-400 mt-0.5 {canDisplayUsernameErrors && !isUsernameValid
							? ''
							: 'hidden'}">{usernameError}</span
					>
				</div>
				<div class="flex flex-col">
					<Label class="mb-2" for="email">Email</Label>
					<Input
						id="email"
						type="email"
						class="shadows-box-level1"
						placeholder="Enter your email"
						required
						bind:value={email}
						disabled={user?.emailConfirmed}
						onblur={handleBlurEmail}
					></Input>
					<span
						class="text-xs text-red-400 mt-0.5 {canDisplayEmailErrors && !isEmailValid
							? ''
							: 'hidden'}">{emailError}</span
					>
				</div>
			</form>
			{#if dialogState === "confirmation_sent"}
				<div
					class="absolute inset-0 flex flex-col items-center justify-center transition-opacity duration-200 ease-out {dialogState !==
					'confirmation_sent'
						? 'opacity-0'
						: 'opacity-100'}"
				>
					<MailCheckIcon class="w-16 h-16 text-foreground" />
					<p class="text-md mt-2 text-center">
						A confirmation link has been sent to your email address <span class="font-semibold"
							>{email}</span
						>. Your name will be changed once you click on the link.
					</p>
				</div>
			{:else if dialogState === "success"}
				<div
					class="absolute inset-0 flex flex-col items-center justify-center transition-opacity duration-200 ease-out {dialogState !==
					'success'
						? 'opacity-0'
						: 'opacity-100'}"
				>
					<CheckCircleIcon class="w-16 h-16 text-emerald-500" />
					<p class="text-md mt-2 text-center">
						Your name has been successfully changed to <span class="font-semibold"
							>{user?.username}</span
						>.
					</p>
				</div>
			{/if}
		</div>

		<Dialog.Footer>
			{#if dialogState === "initial"}
				<Button onclick={handleClickChangeName} class="w-32 shadows-button-bright-level1">
					{#if isCreateSpinnerVisible}
						<Spinner innerCircleColor="#DA448D"></Spinner>
					{:else}
						<span class="pb-[1px]">Change name</span>
					{/if}</Button
				>
			{:else}
				<Button variant="secondary" onclick={handleClickClose}
					><span class="pb-[1px]">Close</span></Button
				>
			{/if}
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
