export async function load({ cookies }) {
	let isTheatreModeCookie = cookies.get("isTheatreMode") ?? "false";
	const isTheatreMode = Boolean(JSON.parse(isTheatreModeCookie));

	let isEpisodeTitleBlurredCookie = cookies.get("isEpisodeTitleBlurred") ?? "true";
	const isEpisodeTitleBlurred = Boolean(JSON.parse(isEpisodeTitleBlurredCookie));

	return {
		isTheatreMode: isTheatreMode,
		isEpisodeTitleBlurred: isEpisodeTitleBlurred
	};
}
