import { getContext, setContext } from "svelte";

export class HeaderState {
	isVideoInputVisible: boolean = $state(false);

	constructor() {}
}

const HEADER_KEY = Symbol("WATCH_APP_HEADER");

export function setHeaderState(): HeaderState {
	return setContext(HEADER_KEY, new HeaderState());
}

export function getHeaderState(): ReturnType<typeof setHeaderState> {
	return getContext<ReturnType<typeof setHeaderState>>(HEADER_KEY);
}
