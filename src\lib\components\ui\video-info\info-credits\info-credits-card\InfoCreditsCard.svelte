<script lang="ts">
	import { isActor, type InfoCreditsCardProps } from "$lib/models/props/infoCreditsCardProps";
	import { AspectRatio } from "$lib/components/ui/aspect-ratio";
	import Logo from "$lib/components/ui/logo/Logo.svelte";

	let { creditsEntry }: InfoCreditsCardProps = $props();
</script>

<div class="flex flex-col w-[92px]">
	<div class="relative w-[92px] rounded-lg overflow-hidden bg-popover">
		<AspectRatio ratio={2 / 3}>
			<div class="absolute inset-0 flex items-center justify-center z-0">
				<Logo size="md" variant="first-letter" className="text-player-neutral-700 opacity-50 pb-2"
				></Logo>
			</div>
			<img
				class="absolute inset-0 w-full h-full object-cover text-transparent z-10 transition-opacity duration-200 {creditsEntry.profileImageUrl
					? 'opacity-100'
					: 'opacity-0'}"
				src={creditsEntry.profileImageUrl}
				alt={creditsEntry.name ?? ""}
			/>
		</AspectRatio>
	</div>

	<p class="mt-1.5 text-xs font-medium leading-tight break-words">{creditsEntry.name ?? ""}</p>
	{#if isActor(creditsEntry)}
		<p class="text-xs text-player-neutral-200 mt-1 leading-tight break-words">
			{creditsEntry.character ?? ""}
		</p>
	{/if}
</div>
