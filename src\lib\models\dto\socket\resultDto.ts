import type { ResultErrorDto } from './resultErrorDto';

interface _SuccessfulResultDto {
	isSuccess: true;
}

interface _SuccessfulValueResultDto<TValue> {
	isSuccess: true;
	readonly value: TValue;
}

interface _FailedResultDto {
	isSuccess: false;
	readonly error: ResultErrorDto;
}

export type ResultDto = _SuccessfulResultDto | _FailedResultDto;
export type ValueResultDto<TValue> = _SuccessfulValueResultDto<TValue> | _FailedResultDto;
