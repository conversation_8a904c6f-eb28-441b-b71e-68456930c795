import { ok, fail, type ValueResult } from "$lib/models/result/result";

export class LocalStorageService {
	public static get<TValue> (key: string): ValueResult<TValue> {
		const jsonString = localStorage.getItem(key);
		if (!jsonString) {
			return fail<TValue>("No local storage value found for the given key");
		}

		const localStorageValue = JSON.parse(jsonString);
		return ok(localStorageValue as TValue);
	}

	public static set (key: string, value: unknown): void {
		const jsonString = JSON.stringify(value);
		localStorage.setItem(key, jsonString);
	}

	public static remove (key: string): void {
		localStorage.removeItem(key);
	}
}
