<script lang="ts">
	import type { VideoLayoutProps } from "$lib/models/props/videoLayoutProps";
	import FullscreenButton from "../buttons/FullscreenButton.svelte";
	import MuteButton from "../buttons/MuteButton.svelte";
	import PlayButton from "../buttons/PlayButton.svelte";
	import TheaterModeButton from "../buttons/TheaterModeButton.svelte";
	import ChapterTitle from "../ChapterTitle.svelte";
	import ErrorCard from "../ErrorCard.svelte";
	import SettingsMenu from "../menus/SettingsMenu.svelte";
	import MutedBanner from "../MutedBanner.svelte";
	import SeekedTimeDisplay from "../SeekedTimeDisplay.svelte";
	import TimeSlider from "../sliders/TimeSlider.svelte";
	import VolumeSlider from "../sliders/VolumeSlider.svelte";
	import TimeGroup from "../TimeGroup.svelte";
	import VideoCaptions from "../VideoCaptions.svelte";
	import VideoGestures from "../VideoGestures.svelte";

	let {
		thumbnails,
		isChapterTitleVisible,
		isTheatreMode,
		isMutedBannerVisible,
		isSubtitleMenuVisible,
		isSubtitleShiftMenuVisible,
		errorMessage,
		onToggleTheatreMode,
		onClickMutedBannerUnmute,
		onSetSubtitleOffsetByUser
	}: VideoLayoutProps = $props();

	let seekedTimeDisplay: SeekedTimeDisplay;

	export function addSeekedSeconds(seconds: number): void {
		seekedTimeDisplay?.addSeekedSeconds(seconds);
	}

	export function resetSeekedSeconds(): void {
		seekedTimeDisplay?.resetSeekedSeconds();
	}
</script>

<SeekedTimeDisplay bind:this={seekedTimeDisplay}></SeekedTimeDisplay>

{#if isMutedBannerVisible}
	<MutedBanner onClickUnmute={onClickMutedBannerUnmute} />
{/if}

<div
	class="pointer-events-none absolute inset-0 z-50 flex h-full w-full items-center justify-center"
>
	<media-spinner
		class="text-white opacity-0 transition-opacity duration-200 ease-linear media-buffering:animate-spin media-buffering:opacity-100 media-error:opacity-0 [&>svg>[data-part='track']]:opacity-25"
		size="96"
		track-width="8"
	></media-spinner>
</div>

<ErrorCard {errorMessage}></ErrorCard>

<!-- Hack to stop the video for pixel shifting when control visibility toggles on Firefox and Chrome -->
<div
	class="absolute inset-0 z-10 w-full h-full flex flex-col backdrop-blur-[0px] opacity-[0.01] pointer-events-none"
></div>

<VideoGestures />
<VideoCaptions />

<media-controls class="opacity-100 absolute inset-0 z-10 flex h-full w-full flex-col">
	<div class="flex-1"></div>
	<div
		class="mx-4 mb-4 controls-container media-controls:opacity-100 opacity-0 media-controls:backdrop-controls-visible backdrop-controls-invisible bg-player-neutral-950/25 rounded-xl shadows-video-layout"
	>
		<media-controls-group class="flex w-full items-center px-2">
			<TimeSlider {thumbnails} />
		</media-controls-group>
		<media-controls-group class="-mt-0.5 flex w-full items-center px-2 pb-2">
			<PlayButton tooltipPlacement="top start" />
			<MuteButton tooltipPlacement="top" />
			<VolumeSlider />
			<TimeGroup />
			{#if isChapterTitleVisible}
				<ChapterTitle />
			{/if}
			<div class="flex-1"></div>
			<!-- <CaptionButton tooltipPlacement="top" /> -->
			<SettingsMenu
				{isSubtitleMenuVisible}
				{isSubtitleShiftMenuVisible}
				{onSetSubtitleOffsetByUser}
				placement="top end"
				tooltipPlacement="top"
			/>
			<TheaterModeButton tooltipPlacement="top" {isTheatreMode} {onToggleTheatreMode}
			></TheaterModeButton>
			<!-- <PIPButton tooltipPlacement="top" /> -->
			<FullscreenButton tooltipPlacement="top end" />
		</media-controls-group>
	</div>
</media-controls>

<style scoped>
	media-controls {
		/* These CSS variables are supported out of the box to easily apply offsets to all tooltips/menus.  */
		--media-tooltip-y-offset: 30px;
		--media-menu-y-offset: 30px;
	}

	media-controls :global(media-volume-slider) {
		--media-slider-preview-offset: 30px;
	}

	.controls-container {
		transition:
			backdrop-filter 250ms ease-out,
			opacity 250ms ease-out;
	}

	.shadows-video-layout {
		box-shadow: inset 0px 1px 0.25px rgba(255, 255, 255, 0.14);
	}
</style>
