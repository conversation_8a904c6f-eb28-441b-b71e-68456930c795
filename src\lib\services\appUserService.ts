import { fail, ok, type Result, type ValueResult } from "$lib/models/result/result";
import type { AppUser } from "$lib/models/appUser";
import { ApiService } from "./apiService";
import { LocalStorageService } from "./localStorageService";
import type { AppUserState } from "$lib/states/appUserState.svelte";
import { CancellationToken, retryResult } from "$lib/utils/retry";
import { displayError } from "$lib/utils/toastUtils";
import type { RerollUsernameResponse } from "$lib/models/dto/responses/rerollUsernameResponse";
import { ResultError } from "$lib/models/result/resultError";
import { UnauthorizedError } from "$lib/models/result/unauthorizedError";
import { StatusCodes } from "$lib/utils/statusCodes";
import { NotFoundError } from "$lib/models/result/notFoundError";
import { InvalidError } from "$lib/models/result/invalidError";
import type { ChangeUsernameResponse } from "$lib/models/dto/responses/changeUsernameResponse";
import type { RecoverUserResponse } from "$lib/models/dto/responses/recoverUserResponse";

const WAIT_FOR_INITIALIZED_USER_DELAY_MS = 1000;
const WAIT_FOR_INITIALIZED_USER_MAX_RETRIES = Number.POSITIVE_INFINITY;

export class AppUserService {
	private static readonly CurrentUserStorageKey = "current_user";
	private static instance: AppUserService;

	private readonly apiService: ApiService;
	private readonly appUserState: AppUserState;

	private currentAppUser?: AppUser;
	private currentResolveUserPromise?: Promise<AppUser>;

	public waitForUserCt?: CancellationToken;

	private constructor(appUserState: AppUserState) {
		this.appUserState = appUserState;
		this.apiService = ApiService.getInstance();
	}

	public static getInstance(): AppUserService {
		if (!AppUserService.instance) {
			throw new Error("AppUserService is not initialized");
		}

		return AppUserService.instance;
	}

	public static setInstance(appUserState: AppUserState): AppUserService {
		AppUserService.instance = new AppUserService(appUserState);
		return AppUserService.instance;
	}

	public async createUser(): Promise<ValueResult<AppUser>> {
		const apiResponse = await this.apiService.createAppUser();
		if (!apiResponse.ok || apiResponse.body === undefined) {
			return fail<AppUser>("Server response failed");
		}

		const user: AppUser = apiResponse.body;
		return ok(user);
	}

	public getCurrentUser(): ValueResult<AppUser> {
		if (this.currentAppUser) {
			return ok(this.currentAppUser);
		}

		const localAppUserResult = LocalStorageService.get<AppUser>(
			AppUserService.CurrentUserStorageKey
		);
		if (localAppUserResult.isFailed) {
			return localAppUserResult;
		}

		const localAppUser = localAppUserResult.value;
		if (!localAppUser || !localAppUser.id || !localAppUser.username || !localAppUser.token) {
			return fail<AppUser>("Failed to get current app user from local storage");
		}

		return localAppUserResult;
	}

	public setCurrentUser(user: AppUser): void {
		this.currentAppUser = user;
		this.appUserState.user = user;
		LocalStorageService.set(AppUserService.CurrentUserStorageKey, user);
	}

	public removeCurrentUser(): void {
		this.currentAppUser = undefined;
		this.appUserState.removeUser();
		LocalStorageService.remove(AppUserService.CurrentUserStorageKey);
	}

	public async getOrCreateCurrentUser(): Promise<ValueResult<AppUser>> {
		const getCurrentUserResult = this.getCurrentUser();
		if (getCurrentUserResult.isFailed) {
			const createUserResult = await this.createUser();
			if (createUserResult.isFailed) {
				return createUserResult;
			}

			return createUserResult;
		}

		return getCurrentUserResult;
	}

	public async waitForInitializedUser(): Promise<AppUser> {
		console.log("waitForInitializedUser");
		if (this.currentAppUser) {
			return this.currentAppUser;
		}

		if (this.currentResolveUserPromise) {
			return this.currentResolveUserPromise;
		}

		this.waitForUserCt?.cancel();
		this.waitForUserCt = new CancellationToken();
		this.currentResolveUserPromise = retryResult<AppUser>(
			() => this.getOrCreateCurrentUser(),
			WAIT_FOR_INITIALIZED_USER_MAX_RETRIES,
			WAIT_FOR_INITIALIZED_USER_DELAY_MS,
			async (e) => {
				displayError("Failed to create current user", e, true);
			},
			this.waitForUserCt
		).then((r) => {
			this.currentResolveUserPromise = undefined;

			if (r.isFailed) {
				return Promise.reject(r.error);
			}

			// In case app user was set before the promise was resolved
			// return the existing app user
			if (this.currentAppUser) {
				return this.currentAppUser;
			}

			this.setCurrentUser(r.value);
			return r.value;
		});

		return this.currentResolveUserPromise;
	}

	public async refreshUser(): Promise<ValueResult<AppUser>> {
		if (!this.currentAppUser) {
			return fail<AppUser>("Current app user not set");
		}

		const apiResponse = await this.apiService.getAppUser(this.currentAppUser.token);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			return fail<AppUser>("Server response failed");
		}

		const user: AppUser = apiResponse.body;
		this.setCurrentUser(user);
		return ok(user);
	}

	public async rerollUsername(): Promise<Result> {
		const getCurrentUserResult = this.getCurrentUser();
		if (getCurrentUserResult.isFailed) {
			return getCurrentUserResult;
		}

		const currentAppUser = getCurrentUserResult.value;
		const apiResponse = await this.apiService.rerollUsername(
			currentAppUser.id,
			currentAppUser.token
		);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.UNAUTHORIZED:
					error = new UnauthorizedError("User is unauthorized").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail(error);
				case StatusCodes.UNPROCESSABLE_ENTITY:
					error = new NotFoundError("User id is invalid").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail(error);
				case StatusCodes.NOT_FOUND:
					error = new NotFoundError("User doesn't exist").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail(error);
				default:
					error = new ResultError(
						`${apiResponse.statusCode}: ${apiResponse.statusText}`
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail(error);
			}
		}

		const rerolledUserDto: RerollUsernameResponse = apiResponse.body;
		const newAppUser: AppUser = {
			id: currentAppUser.id,
			username: rerolledUserDto.username,
			token: rerolledUserDto.token
		};

		this.setCurrentUser(newAppUser);

		return ok();
	}

	public async changeUsername(
		desiredUsername: string,
		email: string
	): Promise<ValueResult<ChangeUsernameResponse>> {
		const getCurrentUserResult = this.getCurrentUser();
		if (getCurrentUserResult.isFailed) {
			return getCurrentUserResult;
		}

		const currentAppUser = getCurrentUserResult.value;
		const apiResponse = await this.apiService.changeUsername(
			currentAppUser.id,
			desiredUsername,
			email,
			currentAppUser.token
		);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.UNAUTHORIZED:
					error = new UnauthorizedError("User is unauthorized").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<ChangeUsernameResponse>(error);
				case StatusCodes.UNPROCESSABLE_ENTITY:
					error = new InvalidError(
						apiResponse.error?.message ?? "Provided data is invalid"
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<ChangeUsernameResponse>(error);
				case StatusCodes.NOT_FOUND:
					error = new NotFoundError("User doesn't exist").withMetadata(
						"statusCode",
						apiResponse.statusCode
					);
					return fail<ChangeUsernameResponse>(error);
				default:
					error = new ResultError(
						`${apiResponse.statusCode}: ${apiResponse.statusText}`
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<ChangeUsernameResponse>(error);
			}
		}

		const changeUsernameDto: ChangeUsernameResponse = apiResponse.body;
		if (changeUsernameDto.user) {
			this.setCurrentUser(changeUsernameDto.user);
		}

		return ok(changeUsernameDto);
	}

	public async recoverUser(email: string): Promise<ValueResult<RecoverUserResponse>> {
		const apiResponse = await this.apiService.recoverUser(email);
		if (!apiResponse.ok || apiResponse.body === undefined) {
			let error: ResultError;
			switch (apiResponse.statusCode) {
				case StatusCodes.UNPROCESSABLE_ENTITY:
					error = new InvalidError(
						apiResponse.error?.message ?? "Provided data is invalid"
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<RecoverUserResponse>(error);
				case StatusCodes.NOT_FOUND:
					error = new NotFoundError(
						apiResponse.error?.message ?? "No user registered with provided email"
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<ChangeUsernameResponse>(error);
				default:
					error = new ResultError(
						`${apiResponse.statusCode}: ${apiResponse.statusText}`
					).withMetadata("statusCode", apiResponse.statusCode);
					return fail<RecoverUserResponse>(error);
			}
		}

		const recoverUserDto: RecoverUserResponse = apiResponse.body;
		return ok(recoverUserDto);
	}
}
