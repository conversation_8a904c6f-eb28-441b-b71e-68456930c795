<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import InfoHeadline from "./InfoHeadline.svelte";

	const { Story } = defineMeta({
		title: "Components/VideoInfo/InfoHeadline",
		component: InfoHeadline
	});
</script>

<Story
	name="WithTitle"
	args={{
		title: "The Bear"
	}}
></Story>

<Story
	name="WithTitleAndTagline"
	args={{
		title: "The Bear",
		tagline: "Back for seconds."
	}}
></Story>

<Story
	name="WithTitleAndEpisodeTitle"
	args={{
		title: "The Bear",
		episodeTitle: "System"
	}}
></Story>

<Story
	name="WithTitleAndEpisode"
	args={{
		title: "The Bear",
		episodeNumber: 1
	}}
></Story>

<Story
	name="WithTitleAndEpisodeAndSeason"
	args={{
		title: "The Bear",
		episodeNumber: 1,
		seasonNumber: 1
	}}
></Story>

<Story
	name="WithTitleAndEpisodeAndSeasonAndEpisodeTitle"
	args={{
		title: "The Bear",
		episodeNumber: 1,
		seasonNumber: 1,
		episodeTitle: "System"
	}}
></Story>
