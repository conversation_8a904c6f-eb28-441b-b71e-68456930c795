<script lang="ts">
	import { isSeriesCredits, type InfoCreditsProps } from "$lib/models/props/infoCreditsProps";
	import * as Accordion from "$lib/components/ui/accordion";
	import type { Actor } from "$lib/models/sourceContainer";
	import InfoCreditsCard from "$lib/components/ui/video-info/info-credits/info-credits-card/InfoCreditsCard.svelte";
	import { Button } from "$lib/components/ui/button";

	const ACTOR_ITEM_WIDTH = 92;
	const ACTOR_ITEM_GAP = 8;
	const ACTOR_ROW_PADDING = 16 + 16;
	const SHOW_ALL_ACTORS_BY_DEFAULT = false;

	let { credits }: InfoCreditsProps = $props();

	let actorsContainerWidth: number | undefined = $state();
	let numRenderedActors: number = $state(Number.MAX_SAFE_INTEGER);
	let showAllActors: boolean = $state(SHOW_ALL_ACTORS_BY_DEFAULT);
	let sortedActors: Actor[] | undefined = $derived(credits.actors?.toSorted(sortActors));
	let sortedGuestActors: Actor[] | undefined = $derived(
		isSeriesCredits(credits) ? credits.guestActors?.toSorted(sortActors) : undefined
	);

	$effect.pre(() => {
		if (showAllActors || !actorsContainerWidth || !sortedActors) {
			return;
		}

		let actorsRowWidth = ACTOR_ROW_PADDING;
		for (let i = 0; i < sortedActors.length; i++) {
			actorsRowWidth += ACTOR_ITEM_WIDTH;
			if (i > 0) {
				actorsRowWidth += ACTOR_ITEM_GAP;
			}

			if (actorsRowWidth > actorsContainerWidth) {
				numRenderedActors = i;
				break;
			}

			if (i === sortedActors.length - 1) {
				numRenderedActors = i + 1;
			}
		}
	});

	function sortActors(a: Actor, b: Actor): number {
		return (a.order ?? Number.MAX_SAFE_INTEGER) - (b.order ?? Number.MAX_SAFE_INTEGER);
	}

	function handleClickShowAllActors() {
		numRenderedActors = Number.MAX_SAFE_INTEGER;
		showAllActors = true;
	}
</script>

<Accordion.Root value={["actors"]} type="multiple">
	{#if sortedActors && sortedActors.length > 0}
		<Accordion.Item value="actors">
			<Accordion.Trigger>Actors</Accordion.Trigger>
			<Accordion.Content>
				<div
					class="flex flex-wrap gap-2 shadows-box-level1 bg-background p-4 rounded-xl"
					bind:clientWidth={actorsContainerWidth}
				>
					{#each sortedActors as actor, i}
						{#if i < numRenderedActors}
							<div><InfoCreditsCard creditsEntry={actor} /></div>
						{/if}
					{/each}
				</div>
				{#if !showAllActors && numRenderedActors < sortedActors?.length}
					<div class="flex justify-center mt-2">
						<Button variant="link" size="sm" onclick={handleClickShowAllActors}
							><span class="pb-[1px]">Show all actors</span></Button
						>
					</div>
				{/if}
			</Accordion.Content>
		</Accordion.Item>
	{/if}

	{#if sortedGuestActors && sortedGuestActors.length > 0}
		<Accordion.Item value="guest-actors">
			<Accordion.Trigger>Guest Actors</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 shadows-box-level1 bg-background p-4 rounded-xl">
					{#each sortedGuestActors as actor}
						<InfoCreditsCard creditsEntry={actor} />
					{/each}
				</div>
			</Accordion.Content>
		</Accordion.Item>
	{/if}

	{#if isSeriesCredits(credits) && credits.creators && credits.creators.length > 0}
		<Accordion.Item value="creators">
			<Accordion.Trigger>Creators</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 shadows-box-level1 bg-background p-4 rounded-xl">
					{#each credits.creators as crew}
						<InfoCreditsCard creditsEntry={crew} />
					{/each}
				</div>
			</Accordion.Content>
		</Accordion.Item>
	{/if}

	{#if credits.writers && credits.writers.length > 0}
		<Accordion.Item value="writers">
			<Accordion.Trigger>Writers</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 shadows-box-level1 bg-background p-4 rounded-xl">
					{#each credits.writers as crew}
						<InfoCreditsCard creditsEntry={crew} />
					{/each}
				</div>
			</Accordion.Content>
		</Accordion.Item>
	{/if}

	{#if credits.directors && credits.directors.length > 0}
		<Accordion.Item value="directors">
			<Accordion.Trigger>Directors</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 shadows-box-level1 bg-background p-4 rounded-xl">
					{#each credits.directors as crew}
						<InfoCreditsCard creditsEntry={crew} />
					{/each}
				</div>
			</Accordion.Content>
		</Accordion.Item>
	{/if}

	{#if credits.originalMusicComposers && credits.originalMusicComposers.length > 0}
		<Accordion.Item value="original-music-composers">
			<Accordion.Trigger>Music Composers</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 shadows-box-level1 bg-background p-4 rounded-xl">
					{#each credits.originalMusicComposers as crew}
						<InfoCreditsCard creditsEntry={crew} />
					{/each}
				</div>
			</Accordion.Content>
		</Accordion.Item>
	{/if}
</Accordion.Root>
