<script lang="ts">
	import Tooltip from "../Tooltip.svelte";
	import type { MuteButtonProps } from "$lib/models/props/MuteButtonProps";
	import MuteIcon from "~icons/media-icons/mute";
	import VolumeLowIcon from "~icons/media-icons/volume-low";
	import VolumeHighIcon from "~icons/media-icons/volume-high";

	let { tooltipPlacement }: MuteButtonProps = $props();
</script>

<Tooltip placement={tooltipPlacement}>
	{#snippet trigger()}
		<media-mute-button
			class="ring-media-focus group relative -mr-1.5 inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg outline-none ring-inset hover:bg-foreground/20 data-[focus]:ring-4"
		>
			<MuteIcon class="hidden h-8 w-8 group-data-[state='muted']:block"></MuteIcon>
			<VolumeLowIcon class="hidden h-8 w-8 group-data-[state='low']:block"></VolumeLowIcon>
			<VolumeHighIcon class="hidden h-8 w-8 group-data-[state='high']:block"></VolumeHighIcon>
		</media-mute-button>
	{/snippet}

	{#snippet content()}
		<span class="media-muted:hidden">Mute</span>
		<span class="media-muted:block hidden">Unmute</span>
	{/snippet}
</Tooltip>
