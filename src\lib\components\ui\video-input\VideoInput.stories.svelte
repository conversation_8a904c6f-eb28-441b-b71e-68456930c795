<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import VideoInput from "./VideoInput.svelte";

	const { Story } = defineMeta({
		title: "Components/VideoInput",
		component: VideoInput,
		argTypes: {
			videoUrl: {
				control: "text"
			}
		}
	});

	const TEST_VIDEO_URL = "https://localhost:3000/video.mp4";
</script>

<script lang="ts">
	import { getAddVideoEvent, setAddVideoEvent } from "$lib/states/videoInputState.svelte";
	import { userEvent, within, expect, fn } from "@storybook/test";
	import type { StoryContext } from "@storybook/svelte";

	setAddVideoEvent();
	const addVideoEvent = getAddVideoEvent();
</script>

<Story name="Primary"></Story>

<Story
	name="WithInputUrl"
	play={async (storyContext: StoryContext) => {
		const { canvasElement } = storyContext;
		const canvas = within(canvasElement);

		await userEvent.type(canvas.getByRole("textbox"), TEST_VIDEO_URL);
	}}
></Story>

<Story
	name="TestAddButton"
	play={async (storyContext: StoryContext) => {
		const { canvasElement } = storyContext;
		const canvas = within(canvasElement);
		const handlerStub = fn();
		addVideoEvent.on(handlerStub);

		await userEvent.type(canvas.getByRole("textbox"), TEST_VIDEO_URL);
		await userEvent.click(canvas.getByText("Add"));

		expect(handlerStub).toHaveBeenCalledOnce();
		expect(handlerStub).toHaveBeenCalledWith(TEST_VIDEO_URL);
	}}
></Story>
