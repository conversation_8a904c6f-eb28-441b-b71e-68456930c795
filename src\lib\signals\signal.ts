export class Signal<T> {
	private handlers: Array<(value: T) => Promise<void>> = [];

	on(handler: (value: T) => Promise<void>): void {
		this.handlers.push(handler);
	}

	off(handler: (value: T) => Promise<void>): void {
		this.handlers = this.handlers.filter((h) => h !== handler);
	}

	async emit(value: T): Promise<void> {
		this.handlers.slice().map((handler) => handler(value));
	}

	clear(): void {
		this.handlers = [];
	}
}
