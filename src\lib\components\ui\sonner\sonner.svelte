<script lang="ts">
	import { Toaster as Sonner, type ToasterProps as SonnerProps } from "svelte-sonner";

	let restProps: SonnerProps = $props();
</script>

<Sonner
	class="toaster group"
	toastOptions={{
		classes: {
			toast:
				"group toast group-[.toaster]:bg-popover/90 backdrop-blur group-[.toaster]:text-foreground group-[.toaster]:border-none group-[.toaster]:shadows-floater-level0",
			description: "group-[.toast]:text-muted-foreground",
			actionButton: "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
			cancelButton: "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"
		}
	}}
	{...restProps}
/>
