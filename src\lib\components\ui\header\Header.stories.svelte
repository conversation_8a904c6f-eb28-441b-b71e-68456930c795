<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import Header from "./Header.svelte";

	const { Story } = defineMeta({
		title: "Components/Header",
		component: Header,
		parameters: {
			layout: "fullscreen"
		}
	});
</script>

<script lang="ts">
	import { getHeaderState, HeaderState, setHeaderState } from "$lib/states/headerState.svelte";
	import { AppUserState, getAppUserState, setAppUserState } from "$lib/states/appUserState.svelte";
	import { sleep } from "$lib/utils/sleep";
	import { onMount } from "svelte";
	import type { StoryContext } from "@storybook/svelte";
	import { userEvent, within } from "@storybook/test";

	setHeaderState();
	const headerState: HeaderState = getHeaderState();

	setAppUserState();
	const userState: AppUserState = getAppUserState();

	onMount(() => {
		// @ts-ignore
		switch (__STORYBOOK_PREVIEW__.currentRender.story.name) {
			case "WithVideoInput":
			case "WithDelayedUserAddition":
				headerState.isVideoInputVisible = true;
				break;
			case "WithVideoInputUrlAndUser":
				headerState.isVideoInputVisible = true;
				userState.user = {
					id: 1,
					username: "Username"
				};
				break;
			case "WithDelayedUserRemoval":
				headerState.isVideoInputVisible = true;
				userState.user = {
					id: 1,
					username: "Username"
				};
				break;
		}
	});
</script>

<Story name="Primary"></Story>

<Story name="WithVideoInput"></Story>

<Story
	name="WithVideoInputUrlAndUser"
	play={async (storyContext: StoryContext) => {
		const { canvasElement } = storyContext;
		const canvas = within(canvasElement);

		await userEvent.type(canvas.getByRole("textbox"), "https://test.com/video.mp4");
	}}
></Story>

<Story
	name="WithDelayedUserAddition"
	play={async () => {
		await sleep(1000);
		userState.user = {
			id: 1,
			username: "Username"
		};
	}}
></Story>

<Story
	name="WithDelayedUserRemoval"
	play={async () => {
		await sleep(1000);
		userState.user = undefined;
	}}
></Story>
