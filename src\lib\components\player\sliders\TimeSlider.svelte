<script lang="ts">
	import type { TimeSliderProps } from "$lib/models/props/timeSliderProps";
	import SliderChapters from "./SliderChapters.svelte";
	import SliderPreview from "./SliderPreview.svelte";
	import SliderThumb from "./SliderThumb.svelte";

	let { thumbnails }: TimeSliderProps = $props();
</script>

<media-time-slider
	class="group relative mx-[7.5px] inline-flex h-10 w-full cursor-pointer touch-none select-none items-center outline-none"
>
	<SliderChapters />
	<SliderThumb />
	<SliderPreview {thumbnails}>
		{#snippet content()}
			<div class="mt-2 text-sm" data-part="chapter-title"></div>
			<media-slider-value
				class="text-[13px] bg-player-neutral-950 px-3 py-2 text-foreground rounded-md"
			></media-slider-value>
		{/snippet}
	</SliderPreview>
</media-time-slider>
