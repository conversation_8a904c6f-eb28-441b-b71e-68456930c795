<script lang="ts">
	import { type InfoExternalLinksProps } from "$lib/models/props/infoExternalLinksProps";
	import * as Tooltip from "$lib/components/ui/tooltip";

	let { imdbId, contentType }: InfoExternalLinksProps = $props();

	let imdbUrl = $derived(`https://www.imdb.com/title/${imdbId}/`);
	let traktUrl = $derived.by(() => {
		if (contentType === "movie") {
			return `https://trakt.tv/movies/${imdbId}`;
		}

		return `https://trakt.tv/episodes/${imdbId}`;
	});
	let letterboxUrl = $derived.by(() => {
		return `https://letterboxd.com/imdb/${imdbId}/`;
	});
</script>

<div class="flex gap-2">
	<Tooltip.Root>
		<Tooltip.Trigger>
			<a href={traktUrl} target="_blank" aria-label="Trakt"
				><svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 48 48"
					><defs
						><radialGradient
							id="a"
							cx="48.46"
							cy="-.95"
							fx="48.46"
							fy="-.95"
							r="64.84"
							gradientUnits="userSpaceOnUse"
							><stop offset="0" stop-color="#9f42c6" /><stop
								offset=".27"
								stop-color="#a041c3"
							/><stop offset=".42" stop-color="#a43ebb" /><stop
								offset=".53"
								stop-color="#aa39ad"
							/><stop offset=".64" stop-color="#b4339a" /><stop
								offset=".73"
								stop-color="#c02b81"
							/><stop offset=".82" stop-color="#cf2061" /><stop
								offset=".9"
								stop-color="#e1143c"
							/><stop offset=".97" stop-color="#f50613" /><stop
								offset="1"
								stop-color="red"
							/></radialGradient
						></defs
					><path
						d="M48 11.26v25.47C48 42.95 42.95 48 36.73 48H11.26C5.04 48 0 42.95 0 36.73V11.26C0 5.04 5.04 0 11.26 0h25.47a11.24 11.24 0 0 1 9.62 5.4c.18.29.34.59.5.89.33.68.6 1.39.79 2.14.1.37.18.76.23 1.15.09.54.13 1.11.13 1.68Z"
						style="fill:url(#a)"
					/><path
						d="m13.62 17.97 7.92 7.92 1.47-1.47-7.92-7.92-1.47 1.47Zm14.39 14.4 1.47-1.46-2.16-2.16L47.64 8.43c-.19-.75-.46-1.46-.79-2.14L24.39 28.75l3.62 3.62Zm-15.09-13.7-1.46 1.46 14.4 14.4 1.46-1.47L23 28.75 46.35 5.4c-.36-.6-.78-1.16-1.25-1.68L21.54 27.28l-8.62-8.61Zm34.95-9.09L28.7 28.75l1.47 1.46L48 12.38v-1.12c0-.57-.04-1.14-.13-1.68ZM25.16 22.27l-7.92-7.92-1.47 1.47 7.92 7.92 1.47-1.47Zm16.16 12.85c0 3.42-2.78 6.2-6.2 6.2H12.88c-3.42 0-6.2-2.78-6.2-6.2V12.88c0-3.42 2.78-6.21 6.2-6.21h20.78V4.6H12.88c-4.56 0-8.28 3.71-8.28 8.28v22.24c0 4.56 3.71 8.28 8.28 8.28h22.24c4.56 0 8.28-3.71 8.28-8.28v-3.51h-2.07v3.51Z"
						style="fill:#fff"
					/></svg
				></a
			>
		</Tooltip.Trigger>
		<Tooltip.Content>Trakt</Tooltip.Content>
	</Tooltip.Root>

	<Tooltip.Root>
		<Tooltip.Trigger>
			<a href={imdbUrl} target="_blank" aria-label="IMDb"
				><svg height="24" viewBox="0 0 987.173 482.96" xmlns="http://www.w3.org/2000/svg"
					><path
						d="M6980.63 3622.19H422.969C189.688 3622.19 0 3432.35 0 3199.07V423.051C0 189.77 189.688 0 422.969 0H6980.63c233.28 0 423.12 189.77 423.12 423.051V3199.07c0 233.28-189.84 423.12-423.12 423.12"
						style="fill:#f1c20e;fill-opacity:1;fill-rule:nonzero;stroke:none"
						transform="matrix(.13333 0 0 -.13333 0 482.96)"
					/><path
						d="M1066.02 2855.08V742.5h547.89v2112.58h-547.89M2783.28 2855.08l-126.95-987.19-77.89 536.57c-23.52 172.03-45 322.58-66.49 450.62h-710.7V742.5h480.24l2.1 1394.69L2485.31 742.5h342.03l192.5 1425.39 1.02-1425.39H3500v2112.58h-716.72M4357.19 2474.06c21.4-12.26 35.86-31.79 42.03-59.37 6.09-26.64 9.22-88.05 9.22-183.28v-818.13c0-141.4-9.22-227.42-27.74-259.22-18.36-31.71-66.48-47.03-145.39-47.03v1386.41c59.38 0 100.32-6.09 121.88-19.38zm-5.16-1731.56c131.1 0 229.38 7.191 295 21.488 64.38 14.293 119.77 38.903 163.75 75.782 44.06 35.941 75.86 86.011 93.21 149.531 18.43 63.439 28.67 190.389 28.67 379.839v741.41c0 199.69-8.21 333.83-19.46 402.42-12.26 67.58-42.96 130.08-92.18 185.32-48.13 55.39-119.77 95.31-212.97 119.76-93.21 24.61-246.8 37.03-512.03 37.03h-408.6V742.5h664.61M5832.81 1254.46c0-101.41-5.15-166.88-15.39-193.52-10.23-26.64-54.29-39.84-88.12-39.84-32.66 0-54.3 12.18-65.47 38.75-10.24 25.62-16.41 85-16.41 178.2v558.12c0 96.33 5.08 155.71 14.38 180.32 10.23 23.51 31.72 35.78 63.44 35.78 33.75 0 78.75-14.38 90.15-41.02 11.25-27.58 17.42-86.01 17.42-175.08zm-713.82 1600.62V742.5h493.67l34.76 134.141c43.99-54.289 93.21-94.141 147.42-121.871 54.38-26.559 134.15-39.918 196.57-39.918 86.09 0 161.79 22.5 224.29 68.589 63.52 45 103.52 99.297 119.93 160.86 17.42 62.339 25.62 156.559 25.62 282.579v592.81c0 127.97-2.11 211.02-8.2 249.84-5.24 38.91-22.66 78.91-50.24 119.93-28.67 40.93-68.59 72.57-121.87 95.23-54.3 22.5-116.72 33.75-189.53 33.75-62.42 0-143.28-12.34-197.58-37.97-53.2-24.53-102.42-62.42-146.41-112.58v687.19h-528.43"
						style="fill:#110f0d;fill-opacity:1;fill-rule:evenodd;stroke:none"
						transform="matrix(.13333 0 0 -.13333 0 482.96)"
					/></svg
				></a
			>
		</Tooltip.Trigger>
		<Tooltip.Content>IMDb</Tooltip.Content>
	</Tooltip.Root>

	{#if contentType === "movie"}
		<Tooltip.Root>
			<Tooltip.Trigger>
				<a href={letterboxUrl} target="_blank" aria-label="Letterboxd">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						xmlns:xlink="http://www.w3.org/1999/xlink"
						style="height: 14px; width: auto"
						viewBox="0 0 378 140"
					>
						<defs>
							<rect id="path-1" x="0" y="0" width="129.847328" height="140"></rect>
							<rect id="path-3" x="0" y="0" width="129.847328" height="140"></rect>
						</defs>
						<g id="dots-neg" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
							<g id="Dots">
								<ellipse id="Green" fill="#00E054" cx="189" cy="70" rx="70.0786517" ry="70"
								></ellipse>
								<g id="Blue" transform="translate(248.152672, 0.000000)">
									<mask id="mask-2" fill="white">
										<use xlink:href="#path-1"></use>
									</mask>
									<g id="Mask"></g>
									<ellipse
										fill="#40BCF4"
										mask="url(#mask-2)"
										cx="59.7686766"
										cy="70"
										rx="70.0786517"
										ry="70"
									></ellipse>
								</g>
								<g id="Orange">
									<mask id="mask-4" fill="white">
										<use xlink:href="#path-3"></use>
									</mask>
									<g id="Mask"></g>
									<ellipse
										fill="#FF8000"
										mask="url(#mask-4)"
										cx="70.0786517"
										cy="70"
										rx="70.0786517"
										ry="70"
									></ellipse>
								</g>
								<path
									d="M129.539326,107.022244 C122.810493,96.2781677 118.921348,83.5792213 118.921348,70 C118.921348,56.3673435 122.810493,43.6683972 129.539326,32.9243209 C136.268159,43.6683972 140.157303,56.3673435 140.157303,70 C140.157303,83.5792213 136.268159,96.2781677 129.539326,107.022244 Z"
									id="Overlap"
									fill="#FFFFFF"
								></path>
								<path
									d="M248.460674,32.9243209 C255.189507,43.6683972 259.078652,56.3673435 259.078652,70 C259.078652,83.5792213 255.189507,96.2781677 248.460674,107.022244 C241.731841,96.2781677 237.842697,83.5792213 237.842697,70 C237.842697,56.3673435 241.731841,43.6683972 248.460674,32.9243209 Z"
									id="Overlap"
									fill="#FFFFFF"
								></path>
							</g>
						</g>
					</svg>
				</a>
			</Tooltip.Trigger>
			<Tooltip.Content>Letterboxd</Tooltip.Content>
		</Tooltip.Root>
	{/if}
</div>
