{"name": "watch.sap.re", "version": "0.0.1", "private": true, "scripts": {"dev": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 vite dev", "build": "vite build", "preview": "cross-env vite preview", "test": "pnpm run test:integration && pnpm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check --plugin-search-dir=. . && eslint .", "format": "prettier --write --plugin-search-dir=. .", "test:integration": "playwright test", "test:unit": "vitest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "knip": "pnpm dlx knip", "chromatic": "npx chromatic --project-token=chpt_92e2d96a1b1ab5b"}, "devDependencies": {"@chromatic-com/storybook": "3.2.6", "@playwright/test": "^1.52.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-svelte-csf": "5.0.0-next.30", "@storybook/blocks": "^8.6.12", "@storybook/svelte": "^8.6.12", "@storybook/sveltekit": "^8.6.12", "@storybook/test": "^8.6.12", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.20.8", "@sveltejs/vite-plugin-svelte": "5.0.3", "@types/eslint": "^9.6.1", "@types/js-cookie": "^3.0.6", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10.4.21", "bits-ui": "1.0.0-next.52", "chromatic": "^11.28.2", "cross-env": "^7.0.3", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-svelte": "3.5.1", "lucide-svelte": "^0.509.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "storybook": "^8.6.12", "svelte": "5.28.2", "svelte-check": "^4.1.7", "svelte-sonner": "^0.3.28", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tslib": "^2.8.1", "typescript": "^5.8.3", "unplugin-icons": "^22.1.0", "vite": "^6.3.5", "vitest": "^3.1.3"}, "type": "module", "dependencies": {"@microsoft/signalr": "^8.0.7", "@phosphor-icons/core": "^2.1.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "js-cookie": "^3.0.5", "media-icons": "^1.1.5", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.3.1", "vidstack": "^1.12.12"}, "pnpm": {"patchedDependencies": {"@storybook/svelte-vite": "patches/@storybook__svelte-vite.patch"}, "onlyBuiltDependencies": ["@storybook/svelte-vite", "@sveltejs/kit", "esbuild", "svelte-preprocess"]}}