<script lang="ts">
	import * as Avatar from "$lib/components/ui/avatar";
	import { RoomUserRoles, type RoomUser } from "$lib/models/roomUser";
	import * as Tooltip from "$lib/components/ui/tooltip";
	import * as Popover from "$lib/components/ui/popover";
	import * as Select from "$lib/components/ui/select";
	import { slide, fly } from "svelte/transition";
	import { getRoomUsersState, RoomUsersState } from "$lib/states/roomUsersState.svelte";
	import RoleIcon from "$lib/components/ui/role-icon/RoleIcon.svelte";
	import UserCard from "../user-card/UserCard.svelte";
	import { getCurrentRoomUserState } from "$lib/states/currentRoomUserState.svelte";
	import type { NewUserListProps } from "$lib/models/props/newUserListProps";
	import { UserAvatarService } from "$lib/services/userAvatarService";
	import { flipY } from "$lib/utils/flipy";

	let { onSetUserRole }: NewUserListProps = $props();

	const userAvatarService = UserAvatarService.getInstance();
	const roomUsersState: RoomUsersState = getRoomUsersState();
	const currentRoomUserState = getCurrentRoomUserState();

	let isAnimatingIn = $state(false);
	let users: RoomUser[] = $derived(roomUsersState.users);
	let canSetRoles: boolean = $derived.by(() => {
		if (!currentRoomUserState.user) {
			return false;
		}

		return currentRoomUserState.user.role >= RoomUserRoles.Moderator;
	});
	let settableRoles: RoomUserRoles[] = $derived.by(() => {
		if (!currentRoomUserState.user) {
			return [];
		}

		return [...Array(currentRoomUserState.user.role).keys()].reverse();
	});

	function canSetUserRoles(user: RoomUser): boolean {
		if (!currentRoomUserState.user) {
			return false;
		}

		return currentRoomUserState.user.role > user.role;
	}

	// https://stackoverflow.com/a/70629246
	function maybe(node: HTMLElement, { from, to }: { from: DOMRect; to: DOMRect }, params: any) {
		if (!isAnimatingIn) {
			return params.fn(node, { from, to }, params);
		}
	}
</script>

<!-- pt-[-2px] is a hack to fix the transitions -->
<div class="flex flex-col pt-[-2px]">
	{#each users as user (user.id)}
		<div
			animate:maybe={{ fn: flipY, duration: 250, delay: 100 }}
			in:slide={{ axis: "y", duration: 250, delay: 0 }}
			out:slide={{ axis: "y", duration: 250, delay: 100 }}
			onintrostart={() => (isAnimatingIn = true)}
			onintroend={() => (isAnimatingIn = false)}
		>
			<div
				in:fly={{ x: 100, duration: 250, delay: 100 }}
				out:fly={{ x: 100, duration: 250, delay: 0 }}
			>
				<Popover.Root>
					<Popover.Trigger
						class="flex items-center space-x-2 w-full relative button bg-accent rounded-lg shadows-button-level1 px-4 py-2 mt-2 focus-visible:outline outline-2 outline-primary/75 outline-offset-2"
					>
						<Avatar.Root class="h-6 w-6">
							<Avatar.Image src={userAvatarService.getAvatarUrl(user.username)} alt={user.username}
							></Avatar.Image>
							<Avatar.Fallback>{user.username[0]}</Avatar.Fallback>
						</Avatar.Root>
						<div>{user.username}</div>

						<Tooltip.Root>
							<Tooltip.Trigger>
								{#if user.role !== RoomUserRoles.Viewer}
									<RoleIcon role={user.role}></RoleIcon>
								{/if}
							</Tooltip.Trigger>
							<Tooltip.Content>{RoomUserRoles[user.role]}</Tooltip.Content>
						</Tooltip.Root>
					</Popover.Trigger>
					<Popover.Content
						onOpenAutoFocus={(e) => {
							e.preventDefault();
						}}
					>
						<UserCard {user}></UserCard>
						{#if canSetRoles && canSetUserRoles(user)}
							<div class="flex items-center justify-between mt-4">
								<span>Role</span>
								<Select.Root
									type="single"
									value={user.role.toString()}
									onValueChange={(value) => onSetUserRole(user, Number.parseInt(value))}
								>
									<Select.Trigger class="ml-4">{RoomUserRoles[user.role]}</Select.Trigger>
									<Select.Content>
										{#each settableRoles as role}
											<Select.Item value={role.toString()}>{RoomUserRoles[role]}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>
						{/if}
					</Popover.Content>
				</Popover.Root>
			</div>
		</div>
	{/each}
</div>
