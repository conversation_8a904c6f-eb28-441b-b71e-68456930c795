@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--media-brand: 245 245 245;
		--media-focus: 78 156 246;

		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;
		--foreground-accent: 14 76% 87%;

		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;

		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;

		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;

		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;

		--primary: 222.2 47.4% 11.2%;
		--primary-foreground: 210 40% 98%;

		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;

		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;

		--destructive: 0 72.2% 50.6%;
		--destructive-foreground: 210 40% 98%;

		--background-box-level0: 240 7% 11%;

		--ring: 222.2 84% 4.9%;

		--radius: 0.5rem;

		--player-neutral-950: 240 20% 1%;
		--player-neutral-900: 240 14% 7%;
		--player-neutral-800: 240 14% 16%;
		--player-neutral-700: 243 14% 25%;
		--player-neutral-600: 240 13% 36%;
		--player-neutral-500: 240 11% 46%;
		--player-neutral-400: 240 12% 57%;
		--player-neutral-300: 240 13% 67%;
		--player-neutral-200: 240 13% 77%;
		--player-neutral-100: 240 12% 87%;
		--player-neutral-50: 240 40% 98%;

		--role-guest: 20 100% 97%;
		--role-viewer: 20 100% 97%;
		--role-moderator: 334 74% 69%;
		--role-administrator: 334 74% 69%;
		--role-creator: 35 50% 56%;

		--background-level0: 240 10% 14%;
	}

	.dark {
		--background: 240 7% 14%;
		--foreground: 20 100% 97%;
		--foreground-accent: 14 76% 87%;

		--muted: 240 12% 57%;
		--muted-foreground: 20 100% 97%;

		--popover: 240 9% 17%;
		--popover-foreground: 20 100% 97%;

		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;

		--border: 240 13% 36%;
		--input: 240 11% 46%;

		--primary: 331 67% 56%;
		--primary-foreground: 20 100% 97%;

		--secondary: 244 11% 26%;
		--secondary-foreground: 20 100% 97%;

		--accent: 244 11% 26%;
		--accent-foreground: 20 100% 97%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;

		--background-box-level0: 240 7% 11%;

		--ring: 240 12% 57%;

		--player-neutral-950: 240 20% 1%;
		--player-neutral-900: 240 14% 7%;
		--player-neutral-800: 240 14% 16%;
		--player-neutral-700: 243 14% 25%;
		--player-neutral-600: 240 13% 36%;
		--player-neutral-500: 240 11% 46%;
		--player-neutral-400: 240 12% 57%;
		--player-neutral-300: 240 13% 67%;
		--player-neutral-200: 240 13% 77%;
		--player-neutral-100: 240 12% 87%;
		--player-neutral-50: 240 40% 98%;

		--role-guest: 20 100% 97%;
		--role-viewer: 20 100% 97%;
		--role-moderator: 334 74% 69%;
		--role-administrator: 334 74% 69%;
		--role-creator: 35 50% 56%;
	}
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}

	media-player video {
		@apply max-h-full;
	}

	.shadows-button-level0 {
		box-shadow:
			0px 6px 8px -4px rgba(0, 0, 0, 0.18),
			0px 0px 1px 1px rgba(0, 0, 0, 0.18),
			inset 0px 1px 0.25px rgba(255, 255, 255, 0.1),
			inset 0px 2px 0.25px rgba(0, 0, 0, 0.06);
	}

	.shadows-button-bright-level0 {
		box-shadow:
			0px 6px 8px -4px rgba(0, 0, 0, 0.18),
			0px 0px 1px 1px rgba(0, 0, 0, 0.3),
			inset 0px 1px 0.25px rgba(255, 255, 255, 0.34),
			inset 0px 2px 0.25px rgba(0, 0, 0, 0.1);
	}

	.shadows-button-bright-level1 {
		box-shadow:
			0px 6px 8px -4px rgba(0, 0, 0, 0.18),
			0px 0px 1px 1px rgba(0, 0, 0, 0.3),
			inset 0px 1px 0.25px rgba(255, 255, 255, 0.3),
			inset 0px 2px 0.25px rgba(0, 0, 0, 0.1);
	}

	.shadows-button-level1 {
		box-shadow:
			0px 6px 8px -4px rgba(0, 0, 0, 0.12),
			0px 0px 1px 1px rgba(0, 0, 0, 0.14),
			inset 0px 1px 0.25px rgba(255, 255, 255, 0.1),
			inset 0px 2px 0.25px rgba(0, 0, 0, 0.06);
	}

	.shadows-button-ghost-level0 {
		box-shadow:
			0px 6px 8px -4px rgba(0, 0, 0, 0.18),
			0px 0px 1px 1px rgba(0, 0, 0, 0.18),
			inset 0px 0px 0.25px 1px rgba(228, 228, 255, 0.23),
			inset 0px 1.5px 0.25px 0.5px rgba(0, 0, 0, 0.06);
	}

	.shadows-floater-level0 {
		box-shadow:
			0px 0px 32px rgba(0, 0, 0, 0.16),
			0px 0px 16px rgba(0, 0, 0, 0.14),
			0px 0px 8px rgba(0, 0, 0, 0.12),
			0px 0px 4px rgba(0, 0, 0, 0.08),
			0px 0px 2px rgba(0, 0, 0, 0.06),
			0px 0px 1px 1px rgba(0, 0, 0, 0.1),
			inset 0px 1px 0.5px rgba(255, 255, 255, 0.06),
			inset 0px 0.5px 0.25px 0.5px rgba(255, 255, 255, 0.05),
			inset 0px 2px 0.25px rgba(0, 0, 0, 0.2);
	}

	.shadows-box-level0 {
		box-shadow:
			0px 1px 0.25px rgba(255, 255, 255, 0.08),
			inset 0px 0.5px 1px 0.5px rgba(0, 0, 0, 0.16);
	}

	.shadows-box-level1 {
		box-shadow:
			0px 1px 0.25px rgba(255, 255, 255, 0.06),
			inset 0px 0.5px 1px 0.5px rgba(0, 0, 0, 0.14);
	}

	.shadows-card-level0 {
		box-shadow:
			0px 0px 1px 1px rgba(0, 0, 0, 0.23),
			inset 0px 0px 0.25px 1px rgba(255, 255, 255, 0.04),
			inset 0px 0px 0.25px 2px rgba(0, 0, 0, 0.06);
	}

	.shadows-separator-level1 {
		box-shadow:
			0px 1px 0.25px rgba(255, 255, 255, 0.06),
			inset 0px 1px 0.25px rgba(0, 0, 0, 0.14);
	}
}

@layer utilities {
	.backdrop-controls-invisible {
		backdrop-filter: blur(12px) opacity(0);
		display: none;
	}

	.backdrop-controls-visible {
		backdrop-filter: blur(12px) opacity(1);
		display: block;
	}
}

/*
 * Prevent FOUC - you don't need this if you're loading styles via link tags or server-side
 * rendering with a framework like Nuxt.
 */
*:not(:defined) {
	opacity: 0;
}

/* .btn {
	@apply text-white text-center leading-none focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-3;
}

.btn-primary {
	@apply bg-blue-600 hover:bg-blue-700;
}

.btn-outline {
	@apply text-blue-500 focus:outline-none border border-blue-500 hover:text-white hover:bg-blue-600;
} */

.font-main-title {
	font-family: "Oswald", sans-serif;
	font-optical-sizing: auto;
	font-weight: 600;
	font-style: normal;
}

.font-subheader {
	font-family: "IBM Plex Mono", monospace;
	font-weight: 500;
	font-style: normal;
}

/* .font-subheader-semibold {
	font-family: "IBM Plex Mono", serif;
	font-weight: 600;
	font-style: normal;
}

.font-subheader-bold {
	font-family: "IBM Plex Mono", serif;
	font-weight: 700;
	font-style: normal;
} */
