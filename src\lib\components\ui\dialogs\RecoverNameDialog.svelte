<script lang="ts">
	import type { AppUser } from "$lib/models/appUser";
	import type { RecoverNameDialogProps } from "$lib/models/props/recoverNameDialogProps";
	import { AppUserService } from "$lib/services/appUserService";
	import { getAppUserState, type AppUserState } from "$lib/states/appUserState.svelte";
	import type { RecoverNameDialogStateType } from "$lib/types/recoverNameDialogStateType";
	import { isEmailAddressValid } from "$lib/utils/isEmailAddressValid";
	import { untrack } from "svelte";
	import * as Dialog from "$lib/components/ui/dialog";
	import { Label } from "$lib/components/ui/label";
	import { Input } from "$lib/components/ui/input";
	import { Button } from "$lib/components/ui/button";
	import Spinner from "$lib/components/ui/spinner/Spinner.svelte";
	import { CircleXIcon, MailCheckIcon } from "lucide-svelte";

	let { isOpen = $bindable() }: RecoverNameDialogProps = $props();

	const appUserState: AppUserState = getAppUserState();
	const appUserService: AppUserService = AppUserService.getInstance();

	let dialogState: RecoverNameDialogStateType = $state("initial");
	let isChangeSpinnerVisible: boolean = $state(false);
	let user: AppUser | undefined = $derived(appUserState.user);
	let email: string = $state("");
	let isEmailValid: boolean = $state(false);
	let emailError: string = $state("");
	let canDisplayEmailErrors: boolean = $state(false);
	let recoveryError: string = $state("");

	$effect(() => {
		email;
		untrack(() => validateEmail(email));
	});

	$effect(() => {
		if (isOpen) {
			untrack(handleOpenDialog);
		} else {
			untrack(handleCloseDialog);
		}
	});

	function validateEmail(email: string) {
		const trimmedEmail = email.trim();
		if (trimmedEmail.length === 0) {
			emailError = "Email is required";
			isEmailValid = false;
			return;
		}

		if (!isEmailAddressValid(trimmedEmail)) {
			emailError = "Invalid email address";
			isEmailValid = false;
			return;
		}

		emailError = "";
		isEmailValid = true;
	}

	async function handleClickRecoverName() {
		if (isChangeSpinnerVisible) {
			return;
		}

		if (!user) {
			return;
		}

		if (!isEmailValid) {
			canDisplayEmailErrors = true;
			return;
		}

		isChangeSpinnerVisible = true;
		const recoverUserResult = await appUserService.recoverUser(email);
		isChangeSpinnerVisible = false;

		if (recoverUserResult.isFailed) {
			recoveryError = recoverUserResult.error.message;
			dialogState = "error";
			return;
		}

		dialogState = "email_sent";
	}

	function handleClickClose() {
		isOpen = false;
	}

	function handleOpenDialog() {
		email = user?.email ?? "";
		dialogState = "initial";
		canDisplayEmailErrors = false;
		recoveryError = "";
	}

	function handleCloseDialog() {}

	function handleBlurEmail() {
		// Have to disable this because there seems to be a bug that
		// causes the input to be blurred as soon as dialog opens.
		// Maybe it's because it has no value?
		// No need for this IF there's only one input field.
		// It will get validated by the button anyways.
		// canDisplayEmailErrors = true;
	}
</script>

<Dialog.Root bind:open={isOpen}>
	<Dialog.Content class="max-w-xs">
		<Dialog.Header>
			<Dialog.Title>Recover name</Dialog.Title>
		</Dialog.Header>

		<div class="relative">
			<form
				class="flex flex-col space-y-2 transition-opacity duration-200 ease-out {dialogState !==
				'initial'
					? 'opacity-0'
					: 'opacity-100'}"
			>
				<div class="text-sm">
					<p class="mt-1">
						Enter the email address you used to register your name, and we will send you a recovery
						link.
					</p>
				</div>

				<div class="flex flex-col pt-2">
					<Label class="mb-2" for="email">Email</Label>
					<Input
						id="email"
						type="email"
						class="shadows-box-level1"
						placeholder="Enter your email"
						required
						bind:value={email}
						onblur={handleBlurEmail}
					></Input>
					<span
						class="text-xs text-red-400 mt-0.5 {canDisplayEmailErrors && !isEmailValid
							? ''
							: 'hidden'}">{emailError}</span
					>
				</div>
			</form>

			{#if dialogState === "email_sent"}
				<div
					class="absolute inset-0 flex flex-col items-center justify-center transition-opacity duration-200 ease-out {dialogState !==
					'email_sent'
						? 'opacity-0'
						: 'opacity-100'}"
				>
					<MailCheckIcon class="w-16 h-16 text-foreground" />
					<p class="text-md mt-2 text-center">
						A recovery link has been sent to your email address <span class="font-semibold"
							>{email}</span
						>. Your name will be restored once you click on the link.
					</p>
				</div>
			{:else if dialogState === "error"}
				<div
					class="absolute inset-0 flex flex-col items-center justify-center transition-opacity duration-200 ease-out {dialogState !==
					'error'
						? 'opacity-0'
						: 'opacity-100'}"
				>
					<CircleXIcon class="w-16 h-16 text-red-500" />
					<p class="text-md mt-2 text-center">
						{recoveryError}
					</p>
				</div>
			{/if}
		</div>

		<Dialog.Footer>
			{#if dialogState === "initial"}
				<Button onclick={handleClickRecoverName} class="w-32 shadows-button-bright-level1">
					{#if isChangeSpinnerVisible}
						<Spinner innerCircleColor="#DA448D"></Spinner>
					{:else}
						<span class="pb-[1px]">Recover name</span>
					{/if}</Button
				>
			{:else}
				<Button variant="secondary" onclick={handleClickClose}
					><span class="pb-[1px]">Close</span></Button
				>
			{/if}
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
