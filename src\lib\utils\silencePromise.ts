import { isPromise } from "./isPromise";

/**
 * https://github.com/videojs/video.js/blob/4238f5c1d88890547153e7e1de7bd0d1d8e0b236/src/js/utils/promise.js
 *
 * Silence a Promise-like object.
 *
 * This is useful for avoiding non-harmful, but potentially confusing "uncaught
 * play promise" rejection error messages.
 *
 * @param  {Object} value
 *         An object that may or may not be `Promise`-like.
 */
export function silencePromise(value: object) {
	if (isPromise(value)) {
		const valueAsPromise = value as Promise<any>;
		valueAsPromise.then(null, (_) => {});
	}
}
