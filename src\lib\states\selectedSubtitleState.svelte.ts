import { getContext, setContext } from "svelte";

export class SelectedSubtitleState {
	src?: string = $state();
	offset?: number = $state();
}

const SELECTED_SUBTITLE_KEY = Symbol("WATCH_APP_SELECTED_SUBTITLE");

export function setSelectedSubtitleState(): SelectedSubtitleState {
	return setContext(SELECTED_SUBTITLE_KEY, new SelectedSubtitleState());
}

export function getSelectedSubtitleState(): ReturnType<typeof setSelectedSubtitleState> {
	return getContext<ReturnType<typeof setSelectedSubtitleState>>(SELECTED_SUBTITLE_KEY);
}
