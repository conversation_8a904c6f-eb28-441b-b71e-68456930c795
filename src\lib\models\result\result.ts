import { ResultError } from './resultError';

interface _Result<TValue, TError extends ResultError> {
	isSuccess: boolean;
	isFailed: boolean;
	toResult(): _Result<never, never>;
	toValueResult<TValue>(): _Result<TValue, TError>;
}

interface _SuccessfulResult extends _Result<never, never> {
	isSuccess: true;
	isFailed: false;
}

interface _SuccessfulValueResult<TValue> extends _Result<TValue, never> {
	isSuccess: true;
	isFailed: false;
	readonly value: TValue;
}

interface _FailedResult<TValue, TError extends ResultError> extends _Result<TValue, TError> {
	isSuccess: false;
	isFailed: true;
	readonly error: TError;
}

class SuccessfulResult implements _SuccessfulResult {
	public readonly isSuccess = true;
	public readonly isFailed = false;

	toResult (): _Result<never, never> {
		return this;
	}

	toValueResult<TValue> (): _Result<TValue, never> {
		throw new Error('Casting a successful result is not supported');
	}
}

class SuccessfulValueResult<TValue> implements _SuccessfulValueResult<TValue> {
	public readonly isSuccess = true;
	public readonly isFailed = false;
	public readonly value: TValue;

	constructor (value: TValue) {
		this.value = value;
	}

	toResult (): _Result<never, never> {
		return new SuccessfulResult();
	}

	public toValueResult<TValue> (): _Result<TValue, never> {
		throw new Error('Casting a successful result is not supported');
	}
}

class FailedResult<TValue, TError extends ResultError> implements _FailedResult<TValue, TError> {
	public readonly isSuccess = false;
	public readonly isFailed = true;
	public readonly error: TError;

	constructor (error: TError) {
		this.error = error;
	}

	toResult (): _Result<never, TError> {
		return this;
	}

	public toValueResult<TValue> (): _Result<TValue, TError> {
		return new FailedResult<TValue, TError>(this.error);
	}
}

export type Result<TError extends ResultError = ResultError> =
	| _SuccessfulResult
	| _FailedResult<never, TError>;

export type ValueResult<TValue, TError extends ResultError = ResultError> =
	| _SuccessfulValueResult<TValue>
	| _FailedResult<TValue, TError>;

export function ok (): Result;
export function ok<TValue> (value: TValue): ValueResult<TValue>;
export function ok<TValue> (value?: TValue): Result | ValueResult<TValue> {
	if (value === undefined) {
		return new SuccessfulResult();
	}

	return new SuccessfulValueResult(value);
}

export function fail<TError extends ResultError = ResultError> (error: TError | string): Result;
export function fail<TValue, TError extends ResultError = ResultError> (
	error: TError | string
): ValueResult<TValue, TError>;
export function fail<TValue, TError extends ResultError = ResultError> (
	error: TError | string
): Result | ValueResult<TValue, TError> {
	if (typeof error === 'string') {
		return new FailedResult<TValue, TError>(new ResultError(error) as TError);
	}

	return new FailedResult<TValue, TError>(error);
}
