<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import InfoCreditsCard from "./InfoCreditsCard.svelte";

	const { Story } = defineMeta({
		title: "Components/VideoInfo/InfoCredits/InfoCreditsCard",
		component: InfoCreditsCard
	});
</script>

<Story name="Primary" args={{ creditsEntry: { name: "Actor" } }} />

<Story
	name="WithOrder"
	args={{
		creditsEntry: {
			name: "Actor",
			order: 0
		}
	}}
/>

<Story
	name="WithCharacter"
	args={{
		creditsEntry: {
			name: "Actor",
			character: "Character"
		}
	}}
/>

<Story
	name="WithFullActor"
	args={{
		creditsEntry: {
			name: "Actor",
			character: "Character",
			order: 0,
			profileImageUrl: "https://image.tmdb.org/t/p/w185/zKUWYrROoa9RvxAggbolQRaSLVG.jpg"
		}
	}}
/>
