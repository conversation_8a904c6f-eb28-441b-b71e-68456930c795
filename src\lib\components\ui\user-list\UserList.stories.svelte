<script module>
	import { defineMeta, setTemplate } from "@storybook/addon-svelte-csf";
	import UserList from "./UserList.svelte";

	const { Story } = defineMeta({
		title: "Components/UserList",
		component: UserList
	});
</script>

<script lang="ts">
	import {
		setRoomUsersState,
		getRoomUsersState,
		RoomUsersState
	} from "$lib/states/roomUsersState.svelte";
	import { onMount } from "svelte";
	import { RoomUserRoles, type RoomUser } from "$lib/models/roomUser";
	import { sleep } from "$lib/utils/sleep";
	import * as Tooltip from "$lib/components/ui/tooltip";
	import { setCurrentRoomUserState } from "$lib/states/currentRoomUserState.svelte";

	setRoomUsersState();
	const roomUsersState: RoomUsersState = getRoomUsersState();

	setCurrentRoomUserState({
		id: 1,
		username: "Username 1",
		role: RoomUserRoles.Creator,
		token: "token"
	});

	setTemplate(template);

	onMount(() => {
		// @ts-ignore
		switch (__STORYBOOK_PREVIEW__.currentRender.story.name) {
			case "WithUsers":
			case "WithDelayedUsersLeaving":
				roomUsersState.users = [
					{
						id: 1,
						username: "Username 1",
						role: RoomUserRoles.Guest,
						token: "token"
					},
					{
						id: 2,
						username: "Username 2",
						role: RoomUserRoles.Viewer,
						token: "token"
					},
					{
						id: 3,
						username: "Username 3",
						role: RoomUserRoles.Moderator,
						token: "token"
					},
					{
						id: 4,
						username: "Username 4",
						role: RoomUserRoles.Administrator,
						token: "token"
					},
					{
						id: 5,
						username: "Username 5",
						role: RoomUserRoles.Creator,
						token: "token"
					}
				];
				break;
			case "WithDelayedUsersJoining":
				roomUsersState.users = [
					{
						id: 2,
						username: "Username 2",
						role: RoomUserRoles.Viewer,
						token: "token"
					},
					{
						id: 4,
						username: "Username 4",
						role: RoomUserRoles.Administrator,
						token: "token"
					}
				];
				break;
		}
	});

	function handleSetUserRole(user: RoomUser, newRole: RoomUserRoles) {
		user.role = newRole;
	}
</script>

{#snippet template()}
	<Tooltip.Provider>
		<UserList onSetUserRole={handleSetUserRole}></UserList>
	</Tooltip.Provider>
{/snippet}

<Story name="Primary"></Story>

<Story name="WithUsers"></Story>

<Story
	name="WithDelayedUsersLeaving"
	play={async () => {
		await sleep(1000);
		roomUsersState.removeById(1);
		await sleep(1000);
		roomUsersState.removeById(3);
		roomUsersState.removeById(5);
	}}
></Story>

<Story
	name="WithDelayedUsersJoining"
	play={async () => {
		await sleep(1000);
		roomUsersState.users = [
			{
				id: 1,
				username: "Username 1",
				role: RoomUserRoles.Guest,
				token: "token"
			},
			{
				id: 2,
				username: "Username 2",
				role: RoomUserRoles.Viewer,
				token: "token"
			},
			{
				id: 4,
				username: "Username 4",
				role: RoomUserRoles.Administrator,
				token: "token"
			}
		];
		await sleep(1000);
		roomUsersState.users = [
			{
				id: 1,
				username: "Username 1",
				role: RoomUserRoles.Guest,
				token: "token"
			},
			{
				id: 2,
				username: "Username 2",
				role: RoomUserRoles.Viewer,
				token: "token"
			},
			{
				id: 3,
				username: "Username 3",
				role: RoomUserRoles.Moderator,
				token: "token"
			},
			{
				id: 4,
				username: "Username 4",
				role: RoomUserRoles.Administrator,
				token: "token"
			},
			{
				id: 5,
				username: "Username 5",
				role: RoomUserRoles.Creator,
				token: "token"
			}
		];
	}}
></Story>
