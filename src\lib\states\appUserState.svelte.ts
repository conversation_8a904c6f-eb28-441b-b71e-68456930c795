import type { AppUser } from "$lib/models/appUser";
import { Signal } from "$lib/signals/signal";
import { getContext, onDestroy, setContext } from "svelte";

const USER_KEY = Symbol("WATCH_APP_USER");

export class AppUserState {
	user: AppUser | undefined = $state();
	rerollUsernameSignal = new Signal<AppUserState>();
	changeUsernameSignal = new Signal<AppUserState>();
	recoverUsernameSignal = new Signal<AppUserState>();

	constructor(user?: AppUser) {
		this.user = user;

		onDestroy(() => {
			this.rerollUsernameSignal.clear();
			this.changeUsernameSignal.clear();
			this.recoverUsernameSignal.clear();
		});
	}

	removeUser(): void {
		this.user = undefined;
	}
}

export function setAppUserState(user?: AppUser): AppUserState {
	return setContext(USER_KEY, new AppUserState(user));
}

export function getAppUserState(): ReturnType<typeof setAppUserState> {
	return getContext<ReturnType<typeof setAppUserState>>(USER_KEY);
}
