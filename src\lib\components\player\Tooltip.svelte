<script lang="ts">
	import type { TooltipProps } from "$lib/models/props/tooltipProps";

	let { placement, trigger, content }: TooltipProps = $props();
</script>

<media-tooltip>
	<media-tooltip-trigger>
		{@render trigger()}
	</media-tooltip-trigger>
	<media-tooltip-content
		class="tooltip animate-out fade-out slide-out-to-bottom-2 data-[visible]:animate-in data-[visible]:fade-in data-[visible]:slide-in-from-bottom-4 z-10 rounded-sm bg-player-neutral-950 px-3 py-2 text-sm font-medium text-foreground"
		{placement}
	>
		{@render content()}
	</media-tooltip-content>
</media-tooltip>

<style>
	:global(media-menu[data-open]) .tooltip {
		display: none !important;
	}
</style>
