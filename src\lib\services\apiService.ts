import { variables } from "$lib/variables";
import { ApiResponse } from "$lib/models/apiResponse";
import type { CreateRoomResponse } from "$lib/models/dto/responses/createRoomResponse";
import type { CreateUserResponse as CreateAppUserResponse } from "$lib/models/dto/responses/createUserResponse";
import type { GetRoomResponse } from "$lib/models/dto/responses/getRoomResponse";
import type { JoinRoomResponse } from "$lib/models/dto/responses/joinRoomResponse";
import type { RerollUsernameResponse } from "$lib/models/dto/responses/rerollUsernameResponse";
import type { GetAppUserResponse } from "$lib/models/dto/responses/getUserResponse";
import type { ChangeUsernameResponse } from "$lib/models/dto/responses/changeUsernameResponse";
import type { VerifyConfirmationTokenResponse } from "$lib/models/dto/responses/verifyConfirmationTokenResponse";
import type { RecoverUserResponse } from "$lib/models/dto/responses/recoverUserResponse";
import type { ErrorDto } from "$lib/models/apiResponseParams";
import type { VerifyRecoveryTokenResponse } from "$lib/models/dto/responses/verifyRecoveryTokenResponse";

let defaultFetchFunction = fetch;
if (typeof window !== "undefined") {
	defaultFetchFunction = fetch.bind(window);
}

export class ApiService {
	private static instance: ApiService;
	private static BaseUrl = variables.ApiBaseUrl;
	private static readonly IdHashTemplate = "idHash";
	private static readonly IdTemplate = "id";
	private static readonly GetRoomEndpoint = `rooms/{${ApiService.IdHashTemplate}}`;
	private static readonly CreateRoomEndpoint = `rooms`;
	private static readonly JoinRoomEndpoint = `rooms/{${ApiService.IdHashTemplate}}/join`;
	private static readonly CreateAppUserEndpoint = `users`;
	private static readonly GetAppUserEndpoint = `users`;
	private static readonly RerollUsernameEndpoint = `users/{${ApiService.IdTemplate}}/username/reroll`;
	private static readonly ChangeUsernameEndpoint = `users/{${ApiService.IdTemplate}}/username`;
	private static readonly VerifyConfirmationTokenEndpoint = `users/{${ApiService.IdTemplate}}/verify`;
	private static readonly RecoverUserEndpoint = `users/recover`;
	private static readonly VerifyRecoverTokenEndpoint = `users/{${ApiService.IdTemplate}}/recover`;

	private readonly fetchFunc: (
		info: RequestInfo,
		init?: RequestInit | undefined
	) => Promise<Response>;

	public constructor(
		fetchFunc: (
			info: RequestInfo,
			init?: RequestInit | undefined
		) => Promise<Response> = defaultFetchFunction
	) {
		this.fetchFunc = fetchFunc;
	}

	public static getInstance(): ApiService {
		if (!ApiService.instance) {
			ApiService.instance = new ApiService();
		}

		return ApiService.instance;
	}

	public createAppUser(): Promise<ApiResponse<CreateAppUserResponse>> {
		const fullUrl = this.getFullUrl(ApiService.CreateAppUserEndpoint);

		return this.post<CreateAppUserResponse>(fullUrl, {});
	}

	public getAppUser(token: string): Promise<ApiResponse<GetAppUserResponse>> {
		const fullUrl = this.getFullUrl(ApiService.GetAppUserEndpoint);

		return this.get<GetAppUserResponse>(fullUrl, token);
	}

	public rerollUsername(id: number, token: string): Promise<ApiResponse<RerollUsernameResponse>> {
		const fullUrl = this.getFullUrl(ApiService.RerollUsernameEndpoint, undefined, id);

		return this.post<RerollUsernameResponse>(fullUrl, {}, token);
	}

	public changeUsername(
		id: number,
		desiredUsername: string,
		email: string,
		token: string
	): Promise<ApiResponse<ChangeUsernameResponse>> {
		const fullUrl = this.getFullUrl(ApiService.ChangeUsernameEndpoint, undefined, id);

		return this.patch<ChangeUsernameResponse>(
			fullUrl,
			{ username: desiredUsername, email: email },
			token
		);
	}

	public recoverUser(email: string): Promise<ApiResponse<RecoverUserResponse>> {
		const fullUrl = this.getFullUrl(ApiService.RecoverUserEndpoint);

		return this.post<RecoverUserResponse>(fullUrl, { email: email });
	}

	public verifyConfirmationToken(
		id: number,
		confirmationToken: string
	): Promise<ApiResponse<VerifyConfirmationTokenResponse>> {
		const fullUrl = this.getFullUrl(ApiService.VerifyConfirmationTokenEndpoint, undefined, id);

		return this.post<VerifyConfirmationTokenResponse>(fullUrl, {
			token: confirmationToken
		});
	}

	public verifyRecoveryToken(
		id: number,
		token: string
	): Promise<ApiResponse<VerifyRecoveryTokenResponse>> {
		const fullUrl = this.getFullUrl(ApiService.VerifyRecoverTokenEndpoint, undefined, id);

		return this.post<VerifyRecoveryTokenResponse>(fullUrl, {
			token: token
		});
	}

	public getRoom(idHash: string): Promise<ApiResponse<GetRoomResponse>> {
		const fullUrl = this.getFullUrl(ApiService.GetRoomEndpoint, idHash);

		return this.get<GetRoomResponse>(fullUrl);
	}

	public async getSvg(url: string): Promise<ApiResponse<string>> {
		try {
			const response = await this.fetchFunc(url);
			if (!response.ok) {
				console.error(
					`Failed to get "${url}". Status: ${response.status} - ${response.statusText}`
				);

				let responseJson: any = {};
				try {
					responseJson = await response.json();
				} catch {}

				let error: ErrorDto | undefined;
				if (responseJson.errors && responseJson.errors.length > 0) {
					error = responseJson.errors[0];
				}
				return new ApiResponse({
					statusCode: response.status,
					statusText: response.statusText,
					error: error
				});
			}

			const svg = await response.text();

			return new ApiResponse<string>({
				statusCode: response.status,
				statusText: response.statusText,
				body: svg
			});
		} catch (e: any) {
			console.error(e);

			return new ApiResponse({
				statusCode: 500,
				statusText: "Unexpected error encountered",
				error: e
			});
		}
	}

	public createRoom(token: string): Promise<ApiResponse<CreateRoomResponse>> {
		const fullUrl = this.getFullUrl(ApiService.CreateRoomEndpoint);

		return this.post<CreateRoomResponse>(fullUrl, {}, token);
	}

	public joinRoom(idHash: string, token: string): Promise<ApiResponse<JoinRoomResponse>> {
		const fullUrl = this.getFullUrl(ApiService.JoinRoomEndpoint, idHash);

		return this.post<JoinRoomResponse>(fullUrl, {}, token);
	}

	private async get<TResponse>(url: string, token?: string): Promise<ApiResponse<TResponse>> {
		try {
			const options: RequestInit = {};
			if (token) {
				options["headers"] = {
					Authorization: `Bearer ${token}`
				};
			}

			const response = await this.fetchFunc(url, options);
			if (!response.ok) {
				console.error(
					`Failed to get "${url}". Status: ${response.status} - ${response.statusText}`
				);

				let responseJson: any = {};
				try {
					responseJson = await response.json();
				} catch {}

				let error: ErrorDto | undefined;
				if (responseJson.errors && responseJson.errors.length > 0) {
					error = responseJson.errors[0];
				}
				return new ApiResponse({
					statusCode: response.status,
					statusText: response.statusText,
					error: error
				});
			}

			const responseJson = await response.json();
			return new ApiResponse({
				statusCode: response.status,
				statusText: response.statusText,
				body: responseJson as TResponse
			});
		} catch (e: any) {
			console.error(e);

			return new ApiResponse({
				statusCode: 500,
				statusText: "Unexpected error encountered",
				error: e
			});
		}
	}

	private async post<TResponse>(
		url: string,
		body: unknown,
		token?: string
	): Promise<ApiResponse<TResponse>> {
		try {
			const options: RequestInit = {
				method: "POST",
				cache: "no-cache",
				headers: new Headers(),
				redirect: "follow",
				body: JSON.stringify(body)
			};

			options.headers = new Headers();
			options.headers.set("Content-Type", "application/json");

			if (token) {
				options.headers.set("Authorization", `Bearer ${token}`);
			}

			const response = await this.fetchFunc(url, options);
			if (!response.ok) {
				console.error(
					`Failed to post "${url}". Status: ${response.status} - ${response.statusText}`
				);

				let responseJson: any = {};
				try {
					responseJson = await response.json();
				} catch {}

				let error: ErrorDto | undefined;
				if (responseJson.errors && responseJson.errors.length > 0) {
					error = responseJson.errors[0];
				}
				return new ApiResponse({
					statusCode: response.status,
					statusText: response.statusText,
					error: error
				});
			}

			const responseJson = await response.json();
			return new ApiResponse({
				statusCode: response.status,
				statusText: response.statusText,
				body: responseJson as TResponse
			});
		} catch (e: any) {
			console.error(e);

			return new ApiResponse({
				statusCode: 500,
				statusText: "Unexpected error encountered",
				error: e
			});
		}
	}

	private async patch<TResponse>(
		url: string,
		body: unknown,
		token?: string
	): Promise<ApiResponse<TResponse>> {
		try {
			const options: RequestInit = {
				method: "PATCH",
				cache: "no-cache",
				headers: new Headers(),
				redirect: "follow",
				body: JSON.stringify(body)
			};

			options.headers = new Headers();
			options.headers.set("Content-Type", "application/json");

			if (token) {
				options.headers.set("Authorization", `Bearer ${token}`);
			}

			const response = await this.fetchFunc(url, options);
			if (!response.ok) {
				console.error(
					`Failed to patch "${url}". Status: ${response.status} - ${response.statusText}`
				);

				let responseJson: any = {};
				try {
					responseJson = await response.json();
				} catch {}

				let error: ErrorDto | undefined;
				if (responseJson.errors && responseJson.errors.length > 0) {
					error = responseJson.errors[0];
				}
				return new ApiResponse({
					statusCode: response.status,
					statusText: response.statusText,
					error: error
				});
			}

			const responseJson = await response.json();
			return new ApiResponse({
				statusCode: response.status,
				statusText: response.statusText,
				body: responseJson as TResponse
			});
		} catch (e: any) {
			console.error(e);

			return new ApiResponse({
				statusCode: 500,
				statusText: "Unexpected error encountered",
				error: e
			});
		}
	}

	private getFullUrl(
		endpoint: string,
		idHash: string | undefined = undefined,
		id: number | undefined = undefined
	) {
		let filledEndpoint = endpoint;
		if (idHash !== undefined) {
			filledEndpoint = this.fillIdHash(filledEndpoint, idHash);
		}

		if (id !== undefined) {
			filledEndpoint = this.fillId(filledEndpoint, id);
		}

		const fullUrl = new URL(filledEndpoint, ApiService.BaseUrl).href;
		return fullUrl;
	}

	private fillIdHash(str: string, idHash: string) {
		return this.replaceTemplate(str, ApiService.IdHashTemplate, idHash);
	}

	private fillId(str: string, id: number) {
		return this.replaceTemplate(str, ApiService.IdTemplate, id.toString());
	}

	private replaceTemplate(str: string, templateName: string, templateValue: string) {
		const regex = new RegExp(`{${templateName}}`, "g");
		return str.replace(regex, templateValue);
	}
}
