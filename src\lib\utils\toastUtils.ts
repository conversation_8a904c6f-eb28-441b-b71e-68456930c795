import type { ResultError } from "$lib/models/result/resultError";
import { toast } from "svelte-sonner";

export function displayError(
	errorText: string,
	errorDescription?: ResultError | Error | string,
	isRetrying?: boolean
) {
	let toastData;
	if (errorDescription) {
		let description;
		if (typeof errorDescription === "string") {
			description = errorDescription;
		} else {
			description = errorDescription.message;
		}

		toastData = {
			description: description
		};

		if (isRetrying) {
			toastData.description += ", retrying...";
		}
	}

	toast.error(errorText, toastData);

	let description;
	if (typeof errorDescription === "string") {
		description = errorDescription;
	} else {
		description = errorDescription?.message;
	}
	console.error(errorText, description);
}

export function displayWarning(warningText: string, warningDescription?: string) {
	toast.warning(warningText, {
		description: warningDescription
	});
	console.warn(warningText, warningDescription);
}
