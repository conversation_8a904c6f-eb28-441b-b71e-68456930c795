<script lang="ts">
	import { type InfoPosterProps } from "$lib/models/props/infoPosterProps";
	import { AspectRatio } from "$lib/components/ui/aspect-ratio";
	import Logo from "$lib/components/ui/logo/Logo.svelte";

	let { posterImageUrl }: InfoPosterProps = $props();
</script>

<div class="relative w-[185px] overflow-hidden bg-popover">
	<AspectRatio ratio={2 / 3}>
		<div class="absolute inset-0 flex items-center justify-center z-0">
			<Logo size="lg" variant="first-letter" className="text-player-neutral-700 opacity-50 pb-4"
			></Logo>
		</div>
		<img
			class="absolute inset-0 w-full h-full object-cover text-transparent z-10 transition-opacity duration-200 {posterImageUrl
				? 'opacity-100'
				: 'opacity-0'}"
			src={posterImageUrl}
			alt="Media poster"
		/>
	</AspectRatio>
</div>
