<script lang="ts">
	import Tooltip from "../Tooltip.svelte";
	import type { CaptionButtonProps } from "$lib/models/props/captionButtonProps";
	import ClosedCaptionsOnIcon from "~icons/media-icons/closed-captions-on";
	import ClosedCaptionsIcon from "~icons/media-icons/closed-captions";

	let { tooltipPlacement }: CaptionButtonProps = $props();
</script>

<Tooltip placement={tooltipPlacement}>
	{#snippet trigger()}
		<media-caption-button
			class="ring-media-focus group relative mr-0.5 inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg outline-none ring-inset hover:bg-foreground/20 data-[focus]:ring-4 aria-hidden:hidden"
		>
			<ClosedCaptionsOnIcon class="media-captions:block hidden h-8 w-8" />
			<ClosedCaptionsIcon class="media-captions:hidden h-8 w-8" />
		</media-caption-button>
	{/snippet}

	{#snippet content()}
		<span class="media-captions:block hidden">Closed-Captions Off</span>
		<span class="media-captions:hidden">Closed-Captions On</span>
	{/snippet}
</Tooltip>
