export interface AnimationFrameRunnerArguments<TContext extends AnimationFrameRunnerContext> {
	onBeforeStart?: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	onFrame: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	onCancelled?: (context: TContext) => void;
	onCompleted?: (context: TContext) => void;
	context: TContext;
}

export interface AnimationFrameRunnerContext {
	isCancellationRequested?: boolean;
	isCompletionRequested?: boolean;
	isCompleted?: boolean;
	isCancelled?: boolean;
}

export class AnimationFrameRunner<TContext extends AnimationFrameRunnerContext> {
	private _onBeforeStart?: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	private _onFrame: (context: TContext, timestamp: DOMHighResTimeStamp) => void;
	private _onCancelled?: (context: TContext) => void;
	private _onCompleted?: (context: TContext) => void;
	private _isOnCancelledRun: boolean;
	private _isOnCompletedRun: boolean;
	private _context: TContext;
	private _rafId: number;

	public constructor(args: AnimationFrameRunnerArguments<TContext>) {
		this._onBeforeStart = args.onBeforeStart;
		this._onFrame = args.onFrame;
		this._onCancelled = args.onCancelled;
		this._onCompleted = args.onCompleted;
		this._isOnCancelledRun = false;
		this._isOnCompletedRun = false;
		this._context = args.context;
		this._rafId = 0;
	}

	public start(): void {
		this._rafId = requestAnimationFrame(this._start.bind(this));
	}

	public cancel(): void {
		cancelAnimationFrame(this._rafId);

		this._handleCancelled();
		this._handleCompleted();
	}

	private _start(timestamp: DOMHighResTimeStamp): void {
		if (this._onBeforeStart) {
			this._onBeforeStart(this._context, timestamp);
		}

		if (this._context.isCancellationRequested) {
			this._handleCancelled();
			this._handleCompleted();

			return;
		}

		if (this._context.isCompletionRequested) {
			this._handleCompleted();

			return;
		}

		requestAnimationFrame(this._frame.bind(this));
	}

	private _frame(timestamp: DOMHighResTimeStamp): void {
		if (this._context.isCancelled || this._context.isCompleted) {
			return;
		}

		this._onFrame(this._context, timestamp);

		if (this._context.isCancellationRequested) {
			this._handleCancelled();
			this._handleCompleted();

			return;
		}

		if (this._context.isCompletionRequested) {
			this._handleCompleted();

			return;
		}

		if (!this._context.isCancelled && !this._context.isCompleted) {
			requestAnimationFrame(this._frame.bind(this));
		}
	}

	private _handleCancelled(): void {
		this._context.isCancelled = true;

		if (this._onCancelled && !this._isOnCancelledRun) {
			this._isOnCancelledRun = true;
			this._onCancelled(this._context);
		}
	}

	private _handleCompleted(): void {
		this._context.isCompleted = true;

		if (this._onCompleted && !this._isOnCompletedRun) {
			this._isOnCompletedRun = true;
			this._onCompleted(this._context);
		}
	}
}
