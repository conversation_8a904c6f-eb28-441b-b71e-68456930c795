import { Prng } from "$lib/utils/prng";

const AVATAR_RADIUS = "50";
const AVATAR_BG_COLOR = [
	"F7D1C5",
	"EBD8BF",
	"FCCBDD",
	"D8D9ED",
	"DADAE2",
	"FFB300",
	"FDD835",
	"FB8C00",
	"E53935",
	"D81B60",
	"757575",
	"3949AB",
	"00897B",
	"546E7A",
	"8E24AA",
	"C0CA33",
	"6D4C41",
	"5E35B1",
	"83839E",
	"817FB7",
	"DA448D",
	"B37909",
	"D15F3B"
];
const RANDOMIZE_IDS = "true";
const BASE_URL = "https://api.dicebear.com/9.x/%avatar_type%/svg?seed=%seed%";
const AVATAR_TYPES = [
	{
		type: "bottts-neutral",
		parts: [
			{
				name: "radius",
				value: AVATAR_RADIUS
			},
			{
				name: "randomizeIds",
				value: RANDOMIZE_IDS
			},
			{
				name: "backgroundColor",
				value: AVATAR_BG_COLOR
			}
		]
	},
	{
		type: "fun-emoji",
		parts: [
			{
				name: "radius",
				value: AVATAR_RADIUS
			},
			{
				name: "randomizeIds",
				value: RANDOMIZE_IDS
			},
			{
				name: "backgroundColor",
				value: AVATAR_BG_COLOR
			}
		]
	}
];

const avatarUrlCache = new Map<string, string>();

export class UserAvatarService {
	private static instance: UserAvatarService;

	public static getInstance() {
		if (!UserAvatarService.instance) {
			UserAvatarService.instance = new UserAvatarService();
		}

		return UserAvatarService.instance;
	}

	public getAvatarUrl(username: string) {
		if (avatarUrlCache.has(username)) {
			return avatarUrlCache.get(username);
		}

		const prng = new Prng(username);
		const avatarType = prng.pick(AVATAR_TYPES);
		if (!avatarType) {
			throw new Error("Failed to get avatar type");
		}

		const baseUrl = BASE_URL.replace("%avatar_type%", avatarType.type).replace("%seed%", username);
		const avatarUrl = new URL(baseUrl);
		avatarType.parts.forEach((part) => {
			if (part.value === undefined) {
				throw new Error(`Failed to get ${part.name} value`);
			}

			const partName = part.name;
			let partValue: string | string[] | undefined = part.value;
			if (Array.isArray(partValue)) {
				partValue = prng.pick(partValue);
			}

			if (!partValue) {
				throw new Error(`Failed to get ${partName} value`);
			}

			avatarUrl.searchParams.set(partName, partValue);
		});

		avatarUrlCache.set(username, avatarUrl.toString());

		return avatarUrl.toString();
	}
}
