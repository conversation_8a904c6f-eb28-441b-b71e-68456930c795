<script lang="ts">
	import { ScrollArea as ScrollAreaPrimitive, type WithoutChild } from "bits-ui";
	import { Scrollbar } from "./index.js";
	import { cn } from "$lib/utils.js";

	const HIDE_SCROLLBAR_TIMEOUT = 2000;

	let {
		ref = $bindable(null),
		class: className,
		orientation = "vertical",
		scrollbarXClasses = "",
		scrollbarYClasses = "",
		hideScrollbarTimeout = HIDE_SCROLLBAR_TIMEOUT,
		children,
		...restProps
	}: WithoutChild<ScrollAreaPrimitive.RootProps> & {
		orientation?: "vertical" | "horizontal" | "both" | undefined;
		scrollbarXClasses?: string | undefined;
		scrollbarYClasses?: string | undefined;
	} & { hideScrollbarTimeout?: number } = $props();

	let isScrollbarVisible: boolean = $state(true);
	let currentVisibilityTimeout: NodeJS.Timeout | undefined = undefined;

	function handleMouseMove() {
		showScrollbar();
		startHideScrollbarTimeout();
	}

	function handleScroll() {
		showScrollbar();
		startHideScrollbarTimeout();
	}

	function showScrollbar() {
		isScrollbarVisible = true;
	}

	function startHideScrollbarTimeout() {
		clearTimeout(currentVisibilityTimeout);
		currentVisibilityTimeout = setTimeout(() => {
			isScrollbarVisible = false;
		}, hideScrollbarTimeout);
	}
</script>

<ScrollAreaPrimitive.Root bind:ref {...restProps} class={cn("relative overflow-hidden", className)}>
	<ScrollAreaPrimitive.Viewport
		class="h-full w-full rounded-[inherit]"
		onmousemove={handleMouseMove}
		onscroll={handleScroll}
	>
		{@render children?.()}
	</ScrollAreaPrimitive.Viewport>
	<div class="transition-opacity duration-200 {isScrollbarVisible ? 'opacity-100' : 'opacity-0'} ">
		{#if orientation === "vertical" || orientation === "both"}
			<Scrollbar orientation="vertical" class={scrollbarYClasses} />
		{/if}
		{#if orientation === "horizontal" || orientation === "both"}
			<Scrollbar orientation="horizontal" class={scrollbarXClasses} />
		{/if}
	</div>
	<ScrollAreaPrimitive.Corner />
</ScrollAreaPrimitive.Root>
