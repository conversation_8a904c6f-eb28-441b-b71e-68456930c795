<script lang="ts">
	import type { PosterProps } from "$lib/models/props/posterProps";
	import { sleep } from "$lib/utils/sleep";
	import { onMount, untrack } from "svelte";
	import type { MediaCrossOrigin } from "vidstack";

	const DisableAnimationMs = 100;
	const NoAnimationDurationClass = "duration-0";
	const AnimationDurationClass = "duration-500";

	let { src, alt, crossOrigin, isVisible }: PosterProps = $props();
	let crossOriginValue: MediaCrossOrigin | undefined = $derived.by(() => {
		if (!crossOrigin) {
			return undefined;
		}

		if (crossOrigin === true) {
			return "";
		}

		return crossOrigin;
	});

	let isLoaded = $state(false);
	let durationClass = $state(NoAnimationDurationClass);
	let srcValue: string | undefined = $state();

	$effect(() => {
		if (src === srcValue) {
			return;
		}

		untrack(() => {
			brieflyDisableAnimation();
			isLoaded = false;
			srcValue = src;
		});
	});

	onMount(() => {
		brieflyDisableAnimation();
	});

	function brieflyDisableAnimation() {
		sleep(DisableAnimationMs).then(() => {
			durationClass = AnimationDurationClass;
		});
	}

	function handleLoad() {
		durationClass = AnimationDurationClass;
		isLoaded = true;
	}
</script>

<div
	class="pointer-events-none absolute inset-0 block h-full w-full opacity-0 transition-opacity {durationClass} ease-in-out [&>img]:h-full [&>img]:w-full [&>img]:object-cover"
	class:opacity-100={isVisible && isLoaded}
>
	<img src={srcValue} {alt} crossorigin={crossOriginValue} onload={handleLoad} />
</div>
