import { sveltekit } from "@sveltejs/kit/vite";
import { vite as vidstack } from "vidstack/plugins";
import { defineConfig } from "vitest/config";
import Icons from "unplugin-icons/vite";
import { FileSystemIconLoader } from "unplugin-icons/loaders";

export default defineConfig({
	plugins: [
		vidstack({ include: /player\// }),
		sveltekit(),
		Icons({
			compiler: "svelte",
			customCollections: {
				"media-icons": FileSystemIconLoader("./node_modules/media-icons/raw"),
				"phosphor-icons-fill": FileSystemIconLoader(
					"./node_modules/@phosphor-icons/core/assets/fill"
				)
			}
		})
	],
	test: {
		include: ["src/**/*.{test,spec}.{js,ts}"]
	}
});
