<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import SetNameDialog from "./SetNameDialog.svelte";
	import { setAppUserState } from "$lib/states/appUserState.svelte";
	import { AppUserService } from "$lib/services/appUserService";

	const { Story } = defineMeta({
		title: "Dialogs/SetNameDialog",
		component: SetNameDialog,
		argTypes: {
			isOpen: {
				control: "boolean",
				defaultValue: false
			}
		}
	});
</script>

<script lang="ts">
	const appUserState = setAppUserState();
	const appUserService = AppUserService.setInstance(appUserState);

	appUserService.setCurrentUser({
		id: 1,
		username: "User1",
		email: "<EMAIL>",
		token: "testToken"
	});
</script>

<Story name="Primary" args={{ isOpen: true }} />

<Story name="WithNameAndEmail" args={{ isOpen: true }} />
