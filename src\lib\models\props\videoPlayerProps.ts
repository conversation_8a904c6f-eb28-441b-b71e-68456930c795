import type { MediaPlayRequestEvent } from "vidstack";
import type { SourceContainer } from "../sourceContainer";

export type MediaPlayerProps = {
	source?: SourceContainer;
	isTheatreMode?: boolean;
	showMutedPlaybackBanner?: boolean;
	showSubtitleShiftMenu?: boolean;
	onToggleTheatreMode?: (isTheatreMode: boolean) => void;
	onPlay?: () => void;
	onPlaying?: () => void;
	onSeeking?: () => void;
	onSeeked?: () => void;
	onEnded?: () => void;
	onPause?: () => void;
	onStalled?: () => void;
	onSuspended?: () => void;
	onWaiting?: () => void;
	onPlayByUser?: (event: MediaPlayRequestEvent) => void;
	onPauseByUser?: () => void;
	onSeekingByUser?: (seconds: number, keyboardSeekDeltaSeconds: number) => void;
	onSetSubtitleOffsetByUser?: (src: string, offset: number) => void;
};
