<script lang="ts">
	import type { SpinnerProps } from "$lib/models/props/spinnerProps";

	let { size = 28, innerCircleColor = "#39384A" }: SpinnerProps = $props();

	let fourthBlurRadius = $derived(Math.max(Math.round(size / 1.75), 1));
	let thirdBlurRadius = $derived(Math.max(Math.round(fourthBlurRadius / 2), 1));
	let secondBlurRadius = $derived(Math.max(Math.round(thirdBlurRadius / 2), 1));
	let firstBlurRadius = $derived(Math.max(Math.round(secondBlurRadius / 2), 1));
	let innerCirclePadding = $derived(Math.max(Math.round(firstBlurRadius / 2), 1));
</script>

<div style="--spinner-size: {size}px;">
	<span class="first-blur" style="--blur-radius: {firstBlurRadius}px;"></span>
	<span class="second-blur" style="--blur-radius: {secondBlurRadius}px;"></span>
	<span class="third-blur" style="--blur-radius: {thirdBlurRadius}px;"></span>
	<span
		class="center-bg-circle"
		style="--inner-circle-color: {innerCircleColor}; --inner-circle-padding: {innerCirclePadding}px;"
	></span>
	<span class="fourth-blur" style="--blur-radius: {fourthBlurRadius}px;"></span>
</div>

<style>
	div {
		position: relative;
		width: var(--spinner-size);
		height: var(--spinner-size);
		border-radius: 50%;
		background: linear-gradient(
			180deg,
			#ffbb56,
			#ffaa56,
			#ff9957,
			#ff8857,
			#ff6e57,
			#ff5763,
			#ff5886,
			#ff58a8
		);
		animation: animate 1s linear infinite;
	}

	@keyframes animate {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	div > span {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 50%;
		background: linear-gradient(
			180deg,
			#ffbb56,
			#ffaa56,
			#ff9957,
			#ff8857,
			#ff6e57,
			#ff5763,
			#ff5886,
			#ff58a8
		);
	}

	div > span:not(.center-bg-circle) {
		filter: blur(var(--blur-radius));
	}

	div > .center-bg-circle::after {
		content: "";
		position: absolute;
		top: var(--inner-circle-padding);
		left: var(--inner-circle-padding);
		right: var(--inner-circle-padding);
		bottom: var(--inner-circle-padding);
		background: var(--inner-circle-color);
		border-radius: 50%;
	}
</style>
