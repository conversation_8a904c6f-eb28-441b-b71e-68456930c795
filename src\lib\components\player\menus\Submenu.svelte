<script lang="ts">
	import type { SubmenuProps } from "$lib/models/props/submenuProps";
	import SubmenuButton from "./SubmenuButton.svelte";

	let { label, hint, icon, content, footer }: SubmenuProps = $props();
</script>

<media-menu>
	<SubmenuButton {label} {hint} {icon}></SubmenuButton>
	<media-menu-items
		class="hidden w-full flex-col items-start justify-center outline-none data-[keyboard]:mt-[3px] data-[open]:inline-block"
	>
		{@render content()}
	</media-menu-items>
	{#if footer}
		{@render footer()}
	{/if}
</media-menu>
