export class ResultError {
	private readonly metadata: Record<string, unknown>;

	message: string;

	constructor (message: string) {
		this.metadata = {};
		this.message = message;
	}

	public withMetadata (key: string, value: unknown): this {
		this.metadata[key] = value;

		return this;
	}

	public hasMetadataKey (key: string): boolean {
		return key in this.metadata;
	}

	public getMetadataValue<TValue> (key: string): TValue {
		return this.metadata[key] as TValue;
	}
}
