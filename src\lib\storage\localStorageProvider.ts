import type { StorageProvider } from "./storageProvider";

export class LocalStorageProvider implements StorageProvider {
	getItem(key: string): string | undefined {
		const value = localStorage.getItem(key);
		if (value === null) {
			return undefined;
		}

		return value;
	}

	setItem(key: string, value: string): void {
		localStorage.setItem(key, value);
	}

	removeItem(key: string): void {
		localStorage.removeItem(key);
	}
}
