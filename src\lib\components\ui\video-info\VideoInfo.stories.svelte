<script module>
	import { defineMeta, setTemplate } from "@storybook/addon-svelte-csf";
	import VideoInfo from "./VideoInfo.svelte";
	import * as Tooltip from "$lib/components/ui/tooltip";

	const { Story } = defineMeta({
		title: "Components/VideoInfo",
		component: VideoInfo
	});
</script>

<script>
	setTemplate(template);
</script>

{#snippet template(args)}
	<Tooltip.Provider>
		<VideoInfo {...args}></VideoInfo>
	</Tooltip.Provider>
{/snippet}

<Story
	name="Primary"
	args={{
		sourceContainer: {
			id: 1,
			type: "mp4",
			video: {
				url: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
				attributes: {
					type: "video/mp4"
				}
			},
			subtitles: [
				{
					url: "https://www.w3schools.com/html/mov_bbb.vtt",
					label: "The.Bear.S01E01.1080p.WEB.H264-CAKES",
					srcLang: "eng",
					attributes: undefined,
					offset: 0
				}
			],
			attributes: undefined,
			externalInfo: {
				type: "series",
				seasonNumber: 1,
				episodeNumber: 1,
				episodeTitle: "System",
				credits: {
					guestActors: [
						{
							character: "Neil Fak",
							order: 5,
							name: "Matty Matheson",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/zKUWYrROoa9RvxAggbolQRaSLVG.jpg"
						},
						{
							character: "Chi-Chi",
							order: 505,
							name: "Christopher J. Zucchero",
							profileImageUrl: "https://image.tmdb.org/t/p/w185None"
						},
						{
							character: "Delivery Guy / Meat Delivery Guy",
							order: 506,
							name: "PJ Fishwick",
							profileImageUrl: "https://image.tmdb.org/t/p/w185None"
						},
						{
							character: "Evil Carrot",
							order: 507,
							name: "Patrick Dunham",
							profileImageUrl: "https://image.tmdb.org/t/p/w185None"
						},
						{
							character: "Phil Ramis",
							order: 508,
							name: "Greg Poljacik",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/djTxAdMAQwCAln8bpIxZyVpGKjK.jpg"
						},
						{
							character: "Robot",
							order: 509,
							name: "Chris McClure",
							profileImageUrl: "https://image.tmdb.org/t/p/w185None"
						},
						{
							character: "Nerd 1",
							order: 510,
							name: "Aaron Crippen",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/6WKNKhiTtCysGf0VwLvTBdGZZcD.jpg"
						},
						{
							character: "Nerd 2",
							order: 511,
							name: "Kai Young",
							profileImageUrl: "https://image.tmdb.org/t/p/w185None"
						},
						{
							character: "Nerd 3",
							order: 512,
							name: "Will Bennett",
							profileImageUrl: "https://image.tmdb.org/t/p/w185None"
						},
						{
							character: "Ebraheim",
							order: 536,
							name: "Edwin Lee Gibson",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/wyDPu35soTnBi4VClpS8fv2atkE.jpg"
						},
						{
							character: "Gary 'Sweeps' Woods",
							order: 537,
							name: "Corey Hendrix",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/jurlf1Ot0mofDsoFTqIBrezx68t.jpg"
						},
						{
							character: "Angel",
							order: 538,
							name: "Jose M. Cervantes",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/yubXHfuuPfqbrZ3PpEegDwwbGaa.jpg"
						},
						{
							character: "Manny",
							order: 539,
							name: "Richard Esteras",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/kepvENRqMTNxdy2Gz9Hch1SJyVc.jpg"
						}
					],
					creators: [
						{
							name: "Christopher Storer",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/kG6I1GlaUtENJoJzrP7uxiJovy9.jpg"
						}
					],
					actors: [
						{
							character: "Carmen 'Carmy' Berzatto",
							order: 0,
							name: "Jeremy Allen White",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/zKk4vmDeUexdevtt6wm8WdMQ1TG.jpg"
						},
						{
							character: "Richard 'Richie' Jerimovich",
							order: 1,
							name: "Ebon Moss-Bachrach",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/xD8GVNayMpiTZxLfahy2DseYcQq.jpg"
						},
						{
							character: "Sydney Adamu",
							order: 2,
							name: "Ayo Edebiri",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/V9TNVjNkAJIiCHLTzcnHLktnPf.jpg"
						},
						{
							character: "Marcus Brooks",
							order: 3,
							name: "Lionel Boyce",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/hpIxX5nkfA3pWCW8rYkEUCSBVyS.jpg"
						},
						{
							character: "Natalie 'Sugar' Berzatto",
							order: 4,
							name: "Abby Elliott",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/v7h08EsTce2pLHkZiaFe1QRuYbU.jpg"
						},
						{
							character: "Bettina 'Tina' Marrero",
							order: 6,
							name: "Liza Colón-Zayas",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/uuFxdf0SlKQfSoZQmEAWYMTyz3e.jpg"
						}
					],
					writers: [
						{
							name: "Christopher Storer",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/kG6I1GlaUtENJoJzrP7uxiJovy9.jpg"
						}
					],
					directors: [
						{
							name: "Christopher Storer",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/kG6I1GlaUtENJoJzrP7uxiJovy9.jpg"
						}
					],
					originalMusicComposers: [
						{
							name: "J.A.Q.",
							profileImageUrl: "https://image.tmdb.org/t/p/w185/mQ4BT0Xsyj4TQVhXzQGFyq5pcaD.jpg"
						}
					]
				},
				originalTitle: "The Bear",
				description:
					"Carmy attempts to retrain the employees of The Original Beef of Chicagoland, but is faced with resistance; in need of back-up, he brings on a talented young chef to help.",
				releaseDate: "2022-06-23",
				posterImageUrl: "https://image.tmdb.org/t/p/w342/l2NFRq7w6azQ2gyG0tDgJdkVACV.jpg",
				backdropImageUrl: "https://image.tmdb.org/t/p/w1280/csnJf7QslLWl2oekdm3mO3INFiq.jpg",
				imdbId: "tt15472204",
				originCountries: ["United States"],
				genres: ["Drama", "Comedy"],
				spokenLanguages: ["English"],
				networks: [
					{
						name: "Hulu",
						logoImageUrl: "https://image.tmdb.org/t/p/original/pqUTCleNUiTLAVlelGxUgWn1ELh.svg"
					}
				],
				productionCompanies: [
					{
						name: "FX Productions",
						logoImageUrl: "https://image.tmdb.org/t/p/original/5cT4zwHA66uNAr2p3CcBDLddXu2.svg"
					}
				],
				title: "The Bear"
			}
		}
	}}
></Story>
