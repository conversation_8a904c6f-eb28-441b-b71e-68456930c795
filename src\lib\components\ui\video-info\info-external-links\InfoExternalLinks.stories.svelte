<script module>
	import { defineMeta, setTemplate } from "@storybook/addon-svelte-csf";
	import InfoExternalLinks from "./InfoExternalLinks.svelte";
	import * as Tooltip from "$lib/components/ui/tooltip";

	const { Story } = defineMeta({
		title: "Components/VideoInfo/InfoExternalLinks",
		component: InfoExternalLinks
	});
</script>

<script>
	setTemplate(template);
</script>

{#snippet template(args)}
	<Tooltip.Provider>
		<InfoExternalLinks {...args}></InfoExternalLinks>
	</Tooltip.Provider>
{/snippet}

<Story
	name="WithMovie"
	args={{
		imdbId: "tt1375666",
		contentType: "movie"
	}}
></Story>

<Story
	name="WithSeries"
	args={{
		imdbId: "tt15472204",
		contentType: "series"
	}}
></Story>
