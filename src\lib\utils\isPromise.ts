/**
 * https://github.com/videojs/video.js/blob/4238f5c1d88890547153e7e1de7bd0d1d8e0b236/src/js/utils/promise.js
 *
 * Returns whether an object is `Promise`-like (i.e. has a `then` method).
 *
 * @param  {Object}  value
 *         An object that may or may not be `Promise`-like.
 *
 * @return {boolean}
 *         Whether or not the object is `Promise`-like.
 */
export function isPromise(value: object): boolean {
	return (
		value !== undefined && value !== null && "then" in value && typeof value.then === "function"
	);
}
