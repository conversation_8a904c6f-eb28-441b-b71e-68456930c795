<script lang="ts">
	import { Accordion as AccordionPrimitive } from "bits-ui";
	import { cn } from "$lib/utils.js";
	import { Separator } from "$lib/components/ui/separator";

	let {
		ref = $bindable(null),
		class: className,
		...restProps
	}: AccordionPrimitive.ItemProps = $props();
</script>

<AccordionPrimitive.Item
	bind:ref
	class={cn("[&[data-state=open]+div]:opacity-0", className)}
	{...restProps}
/>
<Separator class="bg-background shadows-separator-level1"></Separator>
