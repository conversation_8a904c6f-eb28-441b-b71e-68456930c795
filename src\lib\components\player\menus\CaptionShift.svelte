<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import type { CaptionShiftProps } from "$lib/models/props/captionShiftProps";
	import { getSelectedSubtitleState } from "$lib/states/selectedSubtitleState.svelte";

	const INPUT_STEP = 0.05;
	const LEFT_MAJOR_VALUE = -0.5;
	const LEFT_MINOR_VALUE = -0.1;
	const RIGHT_MAJOR_VALUE = 0.5;
	const RIGHT_MINOR_VALUE = 0.1;

	const signFormat = new Intl.NumberFormat("en-US", { signDisplay: "exceptZero" });
	const selectedSubtitleState = getSelectedSubtitleState();

	let { onSetSubtitleOffsetByUser }: CaptionShiftProps = $props();

	let inputPreFocusValue: number | undefined;
	let isDisabled = $derived(selectedSubtitleState.offset === undefined);

	function incrementOffset(value: number) {
		if (selectedSubtitleState.offset === undefined || selectedSubtitleState.src === undefined) {
			return;
		}

		selectedSubtitleState.offset += value;

		onSetSubtitleOffsetByUser?.(selectedSubtitleState.src, selectedSubtitleState.offset);
	}

	function handleKeyDown(e: KeyboardEvent): boolean {
		if (e.key === "Backspace" || e.key === "Delete") {
			return true;
		}

		if (e.key === ".") {
			if (selectedSubtitleState.offset && selectedSubtitleState.offset % 1 !== 0) {
				e.preventDefault();
				return false;
			}

			return true;
		}

		if (isNaN(Number.parseInt(e.key, 10))) {
			e.preventDefault();
			return false;
		}

		return true;
	}

	function handleFocusIn() {
		inputPreFocusValue = selectedSubtitleState.offset;
	}

	function handleFocusOut() {
		if (selectedSubtitleState.src === undefined || selectedSubtitleState.offset === undefined) {
			return;
		}

		if (selectedSubtitleState.offset === inputPreFocusValue) {
			return;
		}

		onSetSubtitleOffsetByUser?.(selectedSubtitleState.src, selectedSubtitleState.offset);
	}
</script>

<div class="flex-col px-3 py-3 bg-player-neutral-900 rounded-lg font-normal w-full">
	<div class="flex items-center justify-between">
		<label for="offset" class={isDisabled ? "opacity-50" : ""}>Offset (s)</label>
		<Input
			id="offset"
			type="number"
			step={INPUT_STEP}
			class="w-28 px-2 h-7 border-player-neutral-700"
			bind:value={selectedSubtitleState.offset}
			onkeydown={handleKeyDown}
			onfocusin={handleFocusIn}
			onfocusout={handleFocusOut}
			disabled={isDisabled}
		></Input>
	</div>
	<div class="flex justify-between mt-2">
		<div class="flex">
			<Button
				size="sm"
				variant="outline"
				class="h-7 px-2 text-sm border-player-neutral-700"
				disabled={isDisabled}
				onclick={() => incrementOffset(LEFT_MAJOR_VALUE)}
				>{signFormat.format(LEFT_MAJOR_VALUE)}</Button
			>
			<Button
				size="sm"
				variant="outline"
				class="h-7 px-2 text-sm ml-2 border-player-neutral-700"
				disabled={isDisabled}
				onclick={() => incrementOffset(LEFT_MINOR_VALUE)}
				>{signFormat.format(LEFT_MINOR_VALUE)}</Button
			>
		</div>
		<div class="flex">
			<Button
				size="sm"
				variant="outline"
				class="h-7 px-2 text-sm border-player-neutral-700"
				disabled={isDisabled}
				onclick={() => incrementOffset(RIGHT_MINOR_VALUE)}
				>{signFormat.format(RIGHT_MINOR_VALUE)}</Button
			>
			<Button
				size="sm"
				variant="outline"
				class="h-7 px-2 text-sm ml-2 border-player-neutral-700"
				disabled={isDisabled}
				onclick={() => incrementOffset(RIGHT_MAJOR_VALUE)}
				>{signFormat.format(RIGHT_MAJOR_VALUE)}</Button
			>
		</div>
	</div>
</div>
