<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import UserCard from "./UserCard.svelte";
	import { RoomUserRoles } from "$lib/models/roomUser";

	const { Story } = defineMeta({
		title: "Components/UserCard",
		component: UserCard,
		argTypes: {
			user: {
				control: "object"
			}
		}
	});
</script>

<Story
	name="Primary"
	args={{
		user: {
			id: 0,
			username: "Userna<PERSON>",
			token: "token"
		}
	}}
></Story>

<Story
	name="WithRole"
	args={{
		user: {
			id: 0,
			username: "Username",
			token: "token",
			role: RoomUserRoles.Moderator
		}
	}}
></Story>
