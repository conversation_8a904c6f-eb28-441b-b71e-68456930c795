import { getContext, onDestroy, setContext } from "svelte";

export class AddVideoEvent {
	private addVideoHandlers: Array<(value: string) => Promise<void>> = [];

	constructor() {
		onDestroy(() => {
			this.addVideoHandlers = [];
		});
	}

	on(handler: (value: string) => Promise<void>): void {
		this.addVideoHandlers.push(handler);
	}

	off(handler: (value: string) => Promise<void>): void {
		this.addVideoHandlers = this.addVideoHandlers.filter((h) => h !== handler);
	}

	async emit(value: string): Promise<void> {
		this.addVideoHandlers.slice().map((handler) => handler(value));
	}

	clear(): void {
		this.addVideoHandlers = [];
	}
}

const VIDEO_INPUT_KEY = Symbol("WATCH_APP_ADD_VIDEO_EVENT");

export function setAddVideoEvent(): AddVideoEvent {
	return setContext(VIDEO_INPUT_KEY, new AddVideoEvent());
}

export function getAddVideoEvent(): ReturnType<typeof setAddVideoEvent> {
	return getContext<ReturnType<typeof setAddVideoEvent>>(VIDEO_INPUT_KEY);
}
