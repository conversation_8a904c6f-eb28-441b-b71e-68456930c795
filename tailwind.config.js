import { fontFamily } from "tailwindcss/defaultTheme";
import tailwindcssAnimate from "tailwindcss-animate";

/** @type {import('tailwindcss').Config} */
const config = {
	darkMode: ["class"],
	content: ["./src/**/*.{html,js,svelte,ts}"],
	future: { hoverOnlyWhenSupported: true },
	safelist: ["dark"],
	theme: {
		container: {
			center: true,
			padding: "2rem",
			screens: {
				"2xl": "1400px"
			}
		},
		extend: {
			colors: {
				"media-brand": "rgb(var(--media-brand) / <alpha-value>)",
				"media-focus": "rgb(var(--media-focus) / <alpha-value>)",
				border: "hsl(var(--border) / <alpha-value>)",
				input: "hsl(var(--input) / <alpha-value>)",
				ring: "hsl(var(--ring) / <alpha-value>)",
				background: "hsl(var(--background) / <alpha-value>)",
				foreground: "hsl(var(--foreground) / <alpha-value>)",
				"foreground-accent": "hsl(var(--foreground-accent) / <alpha-value>)",
				"player-neutral-950": "hsl(var(--player-neutral-950) / <alpha-value>)",
				"player-neutral-900": "hsl(var(--player-neutral-900) / <alpha-value>)",
				"player-neutral-800": "hsl(var(--player-neutral-800) / <alpha-value>)",
				"player-neutral-700": "hsl(var(--player-neutral-700) / <alpha-value>)",
				"player-neutral-600": "hsl(var(--player-neutral-600) / <alpha-value>)",
				"player-neutral-500": "hsl(var(--player-neutral-500) / <alpha-value>)",
				"player-neutral-400": "hsl(var(--player-neutral-400) / <alpha-value>)",
				"player-neutral-300": "hsl(var(--player-neutral-300) / <alpha-value>)",
				"player-neutral-200": "hsl(var(--player-neutral-200) / <alpha-value>)",
				"player-neutral-100": "hsl(var(--player-neutral-100) / <alpha-value>)",
				"player-neutral-50": "hsl(var(--player-neutral-50) / <alpha-value>)",
				"role-guest": "hsl(var(--role-guest) / <alpha-value>)",
				"role-viewer": "hsl(var(--role-viewer) / <alpha-value>)",
				"role-moderator": "hsl(var(--role-moderator) / <alpha-value>)",
				"role-administrator": "hsl(var(--role-administrator) / <alpha-value>)",
				"role-creator": "hsl(var(--role-creator) / <alpha-value>)",
				"background-box-level0": "hsl(var(--background-box-level0) / <alpha-value>)",
				primary: {
					DEFAULT: "hsl(var(--primary) / <alpha-value>)",
					foreground: "hsl(var(--primary-foreground) / <alpha-value>)"
				},
				secondary: {
					DEFAULT: "hsl(var(--secondary) / <alpha-value>)",
					foreground: "hsl(var(--secondary-foreground) / <alpha-value>)"
				},
				destructive: {
					DEFAULT: "hsl(var(--destructive) / <alpha-value>)",
					foreground: "hsl(var(--destructive-foreground) / <alpha-value>)"
				},
				muted: {
					DEFAULT: "hsl(var(--muted) / <alpha-value>)",
					foreground: "hsl(var(--muted-foreground) / <alpha-value>)"
				},
				accent: {
					DEFAULT: "hsl(var(--accent) / <alpha-value>)",
					foreground: "hsl(var(--accent-foreground) / <alpha-value>)"
				},
				popover: {
					DEFAULT: "hsl(var(--popover) / <alpha-value>)",
					foreground: "hsl(var(--popover-foreground) / <alpha-value>)"
				},
				card: {
					DEFAULT: "hsl(var(--card) / <alpha-value>)",
					foreground: "hsl(var(--card-foreground) / <alpha-value>)"
				}
			},
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)"
			},
			fontFamily: {
				sans: ["Inter", ...fontFamily.sans]
			},
			keyframes: {
				"accordion-down": {
					from: { height: "0" },
					to: { height: "var(--bits-accordion-content-height)" }
				},
				"accordion-up": {
					from: { height: "var(--bits-accordion-content-height)" },
					to: { height: "0" }
				},
				"caret-blink": {
					"0%,70%,100%": { opacity: "1" },
					"20%,50%": { opacity: "0" }
				}
			},
			animation: {
				"accordion-down": "accordion-down 0.2s ease-out",
				"accordion-up": "accordion-up 0.2s ease-out",
				"caret-blink": "caret-blink 1.25s ease-out infinite"
			}
		}
	},
	plugins: [
		require("tailwindcss-animate"),
		require("vidstack/tailwind.cjs")({
			prefix: "media",
			webComponents: true
		}),
		customVariants
	]
};

function customVariants({ addVariant, matchVariant }) {
	// Strict version of `.group` to help with nesting.
	matchVariant("parent-data", (value) => `.parent[data-${value}] > &`);

	addVariant("hocus", ["&:hover", "&:focus-visible"]);
	addVariant("group-hocus", [".group:hover &", ".group:focus-visible &"]);
}

export default config;
