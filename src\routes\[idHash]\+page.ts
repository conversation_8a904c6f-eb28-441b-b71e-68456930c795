import { ApiService } from "$lib/services/apiService";
import { RoomService } from "$lib/services/roomService";
import { error, type NumericRange } from "@sveltejs/kit";
import type { PageLoadEvent } from "./$types";
import { isMovieOrSeriesInfo } from "$lib/models/sourceContainer";

export async function load(event: PageLoadEvent) {
	const fetch = event.fetch;
	const idHash = event.params.idHash;
	const isTheatreMode = event.data.isTheatreMode;
	const isEpisodeTitleBlurred = event.data.isEpisodeTitleBlurred;
	const apiService = new ApiService(fetch);
	const roomService = new RoomService(apiService);

	const getRoomResult = await roomService.getRoom(idHash);
	if (getRoomResult.isFailed) {
		if (getRoomResult.error.hasMetadataKey("statusCode")) {
			const statusCode = getRoomResult.error.getMetadataValue<NumericRange<400, 599>>("statusCode");
			throw error(statusCode, getRoomResult.error.message);
		}

		throw error(500, getRoomResult.error.message);
	}

	if (getRoomResult.value.currentSourceContainer?.externalInfo) {
		if ("$external_info_type" in getRoomResult.value.currentSourceContainer.externalInfo) {
			getRoomResult.value.currentSourceContainer.externalInfo.type = getRoomResult.value
				.currentSourceContainer.externalInfo["$external_info_type"] as string;
		}
	}

	let backdropPlaceholderSvg: string | undefined;
	if (
		getRoomResult.value.currentSourceContainer?.externalInfo &&
		isMovieOrSeriesInfo(getRoomResult.value.currentSourceContainer.externalInfo) &&
		getRoomResult.value.currentSourceContainer.externalInfo.backdropPlaceholderUrl
	) {
		let backdropPlaceholderSvgResult = await roomService.getSvg(
			getRoomResult.value.currentSourceContainer.externalInfo.backdropPlaceholderUrl
		);

		if (backdropPlaceholderSvgResult.isSuccess) {
			backdropPlaceholderSvg = backdropPlaceholderSvgResult.value;
		}
	}

	return {
		room: getRoomResult.value,
		isTheatreMode: isTheatreMode,
		isEpisodeTitleBlurred: isEpisodeTitleBlurred,
		backdropPlaceholderSvg: backdropPlaceholderSvg
	};
}
