<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import MediaPlayer from "./MediaPlayer.svelte";

	const { Story } = defineMeta({
		title: "Components/MediaPlayer",
		component: MediaPlayer
	});
</script>

<Story
	name="Primary"
	args={{
		source: {
			id: 1,
			type: "mp4",
			video: {
				url: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
				attributes: { type: "video/mp4" }
			},
			subtitles: [
				{
					url: "https://www.w3schools.com/html/mov_bbb.vtt",
					label: "English",
					srcLang: "en",
					offset: 0
				}
			],
			attributes: { type: "video/mp4" }
		}
	}}
>
	{#snippet children(args)}
		<div class="aspect-video max-h-[80vh] max-w-[80vw]">
			<MediaPlayer {...args} />
		</div>
	{/snippet}
</Story>
