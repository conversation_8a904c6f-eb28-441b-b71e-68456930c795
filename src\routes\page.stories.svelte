<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import IndexPage from "./+page.svelte";
	import RootLayout from "./+layout.svelte";

	const { Story } = defineMeta({
		title: "Pages/Index",
		component: IndexPage,
		// @ts-ignore
		decorators: [() => RootLayout],
		parameters: { layout: "fullscreen" }
	});
</script>

<script lang="ts">
	import { expect, fn, userEvent, within, screen } from "@storybook/test";
	import type { StoryContext } from "@storybook/svelte";
	import { sleep } from "$lib/utils/sleep";
</script>

<Story
	name="Primary"
	parameters={{
		sveltekit_experimental: { stores: { page: { url: new URL("https://localhost/") } } }
	}}
></Story>

<Story
	name="WithJoinParam"
	parameters={{
		sveltekit_experimental: { stores: { page: { url: new URL("https://localhost/?join") } } }
	}}
></Story>

<Story
	name="TestJoinModal"
	parameters={{
		sveltekit_experimental: {
			stores: {
				page: {
					url: new URL("https://localhost/")
				}
			},
			hrefs: {
				"/123456": fn()
			},
			navigation: {
				goto: fn()
			}
		}
	}}
	play={async (storyContext: StoryContext) => {
		const parameters = storyContext.parameters;
		const joinHrefStub = parameters.sveltekit_experimental.hrefs["/123456"];
		const gotoStub = parameters.sveltekit_experimental.navigation.goto;
		const canvasElement = storyContext.canvasElement;
		const canvas = within(canvasElement);

		expect(screen.queryByRole("dialog")).toBeNull();

		await userEvent.click(canvas.getByText("Join"));

		expect(gotoStub).toHaveBeenCalledWith("?join=", { replaceState: false });
		expect(screen.queryByRole("dialog")).toBeInTheDocument();

		await userEvent.type(screen.getByLabelText("Room code"), "123456");
		await userEvent.click(screen.getByRole("link", { name: "Join room" }));

		expect(joinHrefStub).toHaveBeenCalledOnce();

		await userEvent.click(screen.getByRole("button", { name: "Close" }));

		await sleep(500);
		expect(gotoStub).toHaveBeenCalledWith("?", { replaceState: false });
		expect(screen.queryByRole("dialog")).toBeNull();
	}}
></Story>
