<script lang="ts">
	import { type InfoHeadlineProps } from "$lib/models/props/infoHeadlineProps";
	import { CookieStorageProvider } from "$lib/storage/cookieStorageProvider";
	import { PersistentStorage } from "$lib/storage/persistentStorage.svelte";
	import { formatEpisodeNumber } from "$lib/utils/formatEpisodeNumber";

	const isEpisodeTitleBlurredImpl: PersistentStorage<boolean> = new PersistentStorage(
		"isEpisodeTitleBlurred",
		true,
		new CookieStorageProvider()
	);

	let {
		title,
		tagline,
		episodeTitle,
		episodeNumber,
		seasonNumber,
		isEpisodeTitleBlurred
	}: InfoHeadlineProps = $props();

	let prettyEpisodeNumber = $derived.by(() => formatEpisodeNumber(episodeNumber, seasonNumber));

	isEpisodeTitleBlurredImpl.value = isEpisodeTitleBlurred;
</script>

<div class="flex flex-col">
	<span class="font-main-title text-4xl text-foreground-accent">{title}</span>
	<span class="font-subheader text-xl">
		{#if episodeTitle || episodeNumber}
			{#if prettyEpisodeNumber}
				<span>{prettyEpisodeNumber}</span>
			{/if}
			{#if prettyEpisodeNumber && episodeTitle}
				<span>-</span>
			{/if}
			{#if episodeTitle}
				<button
					class={["cursor-pointer", isEpisodeTitleBlurredImpl.value && "blur-[6px]"]}
					onclick={() => (isEpisodeTitleBlurredImpl.value = !isEpisodeTitleBlurredImpl.value)}
					>{episodeTitle}</button
				>
			{/if}
		{:else}
			{tagline ?? ""}
		{/if}
	</span>
</div>
