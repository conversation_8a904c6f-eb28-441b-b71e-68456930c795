<script lang="ts">
	import Header from "$lib/components/ui/header/Header.svelte";
	import { onMount, type Snippet } from "svelte";
	import "../app.css";
	import { setAddVideoEvent } from "$lib/states/videoInputState.svelte";
	import { setHeaderState } from "$lib/states/headerState.svelte";
	import { setAppUserState, AppUserState } from "$lib/states/appUserState.svelte";
	import { ScrollArea } from "$lib/components/ui/scroll-area";
	import { AppUserService } from "$lib/services/appUserService";
	import * as Tooltip from "$lib/components/ui/tooltip";
	import { Toaster } from "$lib/components/ui/sonner";
	import { displayError } from "$lib/utils/toastUtils";
	import { UnauthorizedError } from "$lib/models/result/unauthorizedError";
	import { NotFoundError } from "$lib/models/result/notFoundError";
	import SetNameDialog from "$lib/components/ui/dialogs/SetNameDialog.svelte";
	import RecoverNameDialog from "$lib/components/ui/dialogs/RecoverNameDialog.svelte";

	let { children }: { children: Snippet } = $props();

	setHeaderState();
	setAddVideoEvent();
	const appUserState: AppUserState = setAppUserState();
	const appUserService: AppUserService = AppUserService.setInstance(appUserState);

	let isChangeNameDialogOpen = $state(false);
	let isRecoverUserDialogOpen = $state(false);

	onMount(() => {
		appUserState.rerollUsernameSignal.on(handleRerollUsername);
		appUserState.changeUsernameSignal.on(handleChangeUsername);
		appUserState.recoverUsernameSignal.on(handleRecoverUser);
	});

	async function handleRerollUsername(_: AppUserState): Promise<void> {
		const rerollUsernameResult = await appUserService.rerollUsername();
		if (rerollUsernameResult.isFailed) {
			if (
				rerollUsernameResult.error instanceof UnauthorizedError ||
				rerollUsernameResult.error instanceof NotFoundError
			) {
				appUserService.removeCurrentUser();
				appUserService.waitForInitializedUser();
			}

			displayError("Failed to reroll username", rerollUsernameResult.error);
			return;
		}
	}

	async function handleChangeUsername(_: AppUserState): Promise<void> {
		isChangeNameDialogOpen = true;
	}

	async function handleRecoverUser(_: AppUserState): Promise<void> {
		isRecoverUserDialogOpen = true;
	}
</script>

<Toaster theme="dark"></Toaster>
<SetNameDialog bind:isOpen={isChangeNameDialogOpen}></SetNameDialog>
<RecoverNameDialog bind:isOpen={isRecoverUserDialogOpen}></RecoverNameDialog>

<ScrollArea class="flex flex-col h-screen w-screen">
	<Header></Header>

	<Tooltip.Provider>
		<div class="flex justify-center">
			{@render children()}
		</div>
	</Tooltip.Provider>
</ScrollArea>
