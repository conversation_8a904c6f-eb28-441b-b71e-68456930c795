<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import Logo from "./Logo.svelte";

	const { Story } = defineMeta({
		title: "Components/Logo",
		component: Logo,
		argTypes: {
			variant: {
				options: ["default", "first-letter"],
				control: { type: "radio" }
			},
			size: {
				options: ["sm", "md", "lg"],
				control: { type: "radio" }
			},
			className: {
				control: { type: "text" }
			}
		}
	});
</script>

<Story name="Primary"></Story>

<Story name="First letter" args={{ variant: "first-letter" }}></Story>

<Story name="Small" args={{ size: "sm" }}></Story>

<Story name="Large" args={{ size: "lg" }}></Story>

<Story name="Custom color" args={{ className: "text-red-500" }}></Story>
