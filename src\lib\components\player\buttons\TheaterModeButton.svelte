<script lang="ts">
	import type { TheaterModeButtonProps } from "$lib/models/props/theatreModeButtonProps";
	import ArrowsOutLineHorizontalIcon from "~icons/phosphor-icons-fill/arrows-out-line-horizontal-fill";
	import ArrowsInLineHorizontalIcon from "~icons/phosphor-icons-fill/arrows-in-line-horizontal-fill";
	import Tooltip from "../Tooltip.svelte";

	let { tooltipPlacement, isTheatreMode, onToggleTheatreMode }: TheaterModeButtonProps = $props();

	function toggleTheaterMode() {
		isTheatreMode = !isTheatreMode;
		onToggleTheatreMode?.(isTheatreMode);
	}
</script>

<Tooltip placement={tooltipPlacement}>
	{#snippet trigger()}
		<button
			type="button"
			class="ring-media-focus group relative inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg outline-none ring-inset hover:bg-foreground/20 data-[focus]:ring-4 aria-hidden:hidden"
			onclick={toggleTheaterMode}
		>
			{#if isTheatreMode}
				<ArrowsInLineHorizontalIcon class="size-7"></ArrowsInLineHorizontalIcon>
			{:else}
				<ArrowsOutLineHorizontalIcon class="size-7"></ArrowsOutLineHorizontalIcon>
			{/if}
		</button>
	{/snippet}

	{#snippet content()}
		{#if isTheatreMode}
			<span>Default View</span>
		{:else}
			<span>Theatre View</span>
		{/if}
	{/snippet}
</Tooltip>
