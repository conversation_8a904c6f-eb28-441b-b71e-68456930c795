<script lang="ts">
	import { AppUserService } from "$lib/services/appUserService.js";
	import { CheckCircleIcon, CircleXIcon } from "lucide-svelte";
	import { onMount } from "svelte";

	let { data } = $props();

	const appUserService = AppUserService.getInstance();

	onMount(() => {
		if (data.user) {
			appUserService.setCurrentUser(data.user);
		} else {
			appUserService.waitForInitializedUser();
		}
	});
</script>

<svelte:head>
	<meta property="og:site_name" content="watch" />
	<title>Verify email - watch</title>
</svelte:head>

<div class="flex flex-col justify-center items-center h-full content-verification">
	<div
		class="flex flex-col items-center text-center mt-[-5rem] max-w-96 rounded-xl px-10 py-8 bg-popover shadows-floater-level0"
	>
		{#if !data.error}
			<CheckCircleIcon class="w-28 h-28 text-emerald-500" />
			<p class="text-lg mt-5">
				<span
					>Thank you, your email address <span class="font-semibold">{data.user?.email}</span> has been
				</span>
				{#if data.isUsernameSet}
					<span>
						verified and your name has been changed to
						<span class="font-semibold">{data.user.username}</span>!</span
					>
				{:else}
					<span>verified!</span>
				{/if}
			</p>
			{#if data.usernameError}
				<p class="mt-4 text-lg text-red-400">
					Unfortunately, setting your name resulted in the following error: <span
						class="font-semibold">{data.usernameError}</span
					>
				</p>
			{/if}
			<p class="mt-4 text-lg">You can now close this window and resume your watching.</p>
		{:else}
			<CircleXIcon class="w-28 h-28 text-red-500" />
			<p class="text-lg mt-5">
				<span
					>Sorry, we failed to verify your email address with the following error: <span
						class="font-semibold">{data.error}</span
					>
				</span>
			</p>
			<p class="mt-5">
				<span
					>Verification tokens are only valid for a limited time. Please try requesting a new one.</span
				>
			</p>
		{/if}
	</div>
</div>

<style>
	.content-verification {
		min-height: calc(100vh - 5rem);
	}
</style>
