<script lang="ts">
	import { Select as SelectPrimitive, type WithoutChild } from "bits-ui";
	import { cn } from "$lib/utils.js";
	import { ChevronsUpDownIcon } from "lucide-svelte";

	let {
		ref = $bindable(null),
		class: className,
		children,
		...restProps
	}: WithoutChild<SelectPrimitive.TriggerProps> = $props();
</script>

<SelectPrimitive.Trigger
	bind:ref
	class={cn(
		"shadows-button-level1 bg-accent data-[placeholder]:text-muted-foreground flex h-10 w-full items-center justify-between rounded-lg px-3 py-2 text-sm focus-visible:outline outline-2 outline-primary/75 outline-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
		className
	)}
	{...restProps}
>
	{@render children?.()}
	<ChevronsUpDownIcon class="size-4 opacity-50" />
</SelectPrimitive.Trigger>
