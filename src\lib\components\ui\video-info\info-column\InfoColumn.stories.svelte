<script module>
	import { defineMeta, setTemplate } from "@storybook/addon-svelte-csf";
	import InfoColumn from "./InfoColumn.svelte";
	import * as Tooltip from "$lib/components/ui/tooltip";

	const { Story } = defineMeta({
		title: "Components/VideoInfo/InfoColumn",
		component: InfoColumn
	});
</script>

<script>
	setTemplate(template);
</script>

{#snippet template(args)}
	<Tooltip.Provider>
		<InfoColumn {...args}></InfoColumn>
	</Tooltip.Provider>
{/snippet}

<Story name="Primary" args={{ items: [{ title: "Title", contentItems: [{ value: "Value" }] }] }} />

<Story
	name="WithMultipleItems"
	args={{
		items: [
			{ title: "Title", contentItems: [{ value: "Value" }] },
			{ title: "Title Two", contentItems: [{ value: "Value, Value Two" }] },
			{ title: "Title Three", contentItems: [{ value: "Value, Value Two, Value Three" }] }
		]
	}}
/>

<Story
	name="WithTwoPresentAndOneMissingContentItems"
	args={{
		items: [
			{ title: "Title", contentItems: [{ value: "Value" }] },
			{ title: "Title Two", contentItems: [{ value: "Value, Value Two" }] },
			{ title: "Title Three", contentItems: [] }
		]
	}}
/>

<Story
	name="WithMultipleItemsAndImages"
	args={{
		items: [
			{
				title: "Title",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					}
				]
			},
			{
				title: "Title Two",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					},
					{
						value: "Syncopy",
						imageUrl: "https://image.tmdb.org/t/p/original/3tvBqYsBhxWeHlu62SIJ1el93O7.svg"
					}
				]
			},
			{
				title: "Title Three",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					},
					{
						value: "Syncopy",
						imageUrl: "https://image.tmdb.org/t/p/original/3tvBqYsBhxWeHlu62SIJ1el93O7.svg"
					},
					{
						value: "Warner Bros. Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/zhD3hhtKB5qyv7ZeL4uLpNxgMVU.svg"
					}
				]
			},
			{
				title: "Title Four",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					},
					{
						value: "Syncopy",
						imageUrl: "https://image.tmdb.org/t/p/original/3tvBqYsBhxWeHlu62SIJ1el93O7.svg"
					},
					{
						value: "Warner Bros. Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/zhD3hhtKB5qyv7ZeL4uLpNxgMVU.svg"
					},
					{
						value: "Téléfilm Canada",
						imageUrl: "https://image.tmdb.org/t/p/original/kcNh09qqR2Pkw1JAzRW4CommrW4.svg"
					}
				]
			}
		]
	}}
/>

<Story
	name="WithMultipleItemsAndImagesMixed"
	args={{
		items: [
			{
				title: "Title",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					}
				]
			},
			{
				title: "Title Two",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					},
					{
						value: "Syncopy"
					}
				]
			},
			{
				title: "Title Three",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					},
					{
						value: "Syncopy"
					},
					{
						value: "Warner Bros. Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/zhD3hhtKB5qyv7ZeL4uLpNxgMVU.svg"
					}
				]
			},
			{
				title: "Title Four",
				contentItems: [
					{
						value: "Legendary Pictures",
						imageUrl: "https://image.tmdb.org/t/p/original/8M99Dkt23MjQMTTWukq4m5XsEuo.svg"
					},
					{
						value: "Syncopy"
					},
					{
						value: "Warner Bros. Pictures"
					},
					{
						value: "Téléfilm Canada",
						imageUrl: "https://image.tmdb.org/t/p/original/kcNh09qqR2Pkw1JAzRW4CommrW4.svg"
					}
				]
			}
		]
	}}
/>
