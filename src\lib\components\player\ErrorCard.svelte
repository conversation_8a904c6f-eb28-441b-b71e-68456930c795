<script lang="ts">
	import type { ErrorCardProps } from "$lib/models/props/errorCardProps";
	import { CircleXIcon } from "lucide-svelte";

	let { errorMessage }: ErrorCardProps = $props();
</script>

<div
	class="absolute inset-0 flex flex-col z-50 opacity-0 media-error:opacity-100 pointer-events-none"
>
	<div class="flex">
		<div class="flex-1"></div>
		<div
			class="flex flex-col items-center justify-center w-fit bg-player-neutral-950/90 backdrop-blur rounded-xl px-6 py-4 mx-6 my-4 shadow-muted-banner"
		>
			<CircleXIcon class="size-12 text-rose-600" />
			<span class="text-rose-500 mt-2">Playback failed: {errorMessage}</span>
		</div>
		<div class="flex-1"></div>
	</div>
	<div class="flex-1"></div>
</div>

<style>
	.shadow-muted-banner {
		box-shadow: inset 0px 1px 0.25px rgba(255, 255, 255, 0.18);
	}
</style>
