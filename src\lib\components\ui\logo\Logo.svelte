<script lang="ts">
	import type { LogoProps } from "$lib/models/props/logoProps";

	let { variant = "default", size = "md", className }: LogoProps = $props();

	let fontSizeClass = $derived.by(() => {
		switch (size) {
			case "sm":
				return "text-3xl";
			case "md":
				return "text-6xl";
			case "lg":
				return "text-8xl";
			default:
				console.error(`Invalid logo size: ${size}`);
		}
	});

	let logoText = $derived.by(() => {
		switch (variant) {
			case "default":
				return "watch";
			case "first-letter":
				return "w";
			default:
				console.error(`Invalid logo variant: ${variant}`);
		}
	});
</script>

<span
	class="select-none inline-block align-text-top leading-none {variant === 'first-letter'
		? 'pr-1'
		: ''} {fontSizeClass} {className ? className : 'text-transparent gradient'}">{logoText}</span
>

<style>
	span {
		font-family: "<PERSON><PERSON>", system-ui;
		font-weight: 700;
		font-style: normal;
	}

	.gradient {
		background: linear-gradient(
			180deg,
			#ffbb56,
			#ffaa56,
			#ff9957,
			#ff8857,
			#ff6e57,
			#ff5763,
			#ff5886,
			#ff58a8
		);
		background-clip: text;
	}
</style>
