export interface PlaybackInfo {
	playbackState: PlaybackState;
	currentTimeInSeconds: number;
	playbackStateSource?: BasePlaybackStateSource;
}

export enum PlaybackState {
	Stopped = 0,
	Paused,
	Playing
}

export interface BasePlaybackStateSource {
	type: "play" | "pause" | "seek" | "seekByKeyboard";
}

export interface PlayPlaybackStateSource extends BasePlaybackStateSource {
	type: "play";
}

export interface PausePlaybackStateSource extends BasePlaybackStateSource {
	type: "pause";
}

export interface SeekPlaybackStateSource extends BasePlaybackStateSource {
	type: "seek";
}

export interface SeekByKeyboardPlaybackStateSource extends BasePlaybackStateSource {
	type: "seekByKeyboard";
	keyboardSeekDeltaSeconds: number;
}

export function isSeekByKeyboardSource(
	playbackStateSource?: BasePlaybackStateSource
): playbackStateSource is SeekByKeyboardPlaybackStateSource {
	if (!playbackStateSource) {
		return false;
	}

	return playbackStateSource.type === "seekByKeyboard";
}
