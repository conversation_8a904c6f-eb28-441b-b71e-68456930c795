<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import InfoPoster from "./InfoPoster.svelte";

	const { Story } = defineMeta({
		title: "Components/VideoInfo/InfoPoster",
		component: InfoPoster
	});
</script>

<Story
	name="Primary"
	args={{
		posterImageUrl: "https://image.tmdb.org/t/p/w342/l2NFRq7w6azQ2gyG0tDgJdkVACV.jpg"
	}}
></Story>

<Story
	name="Non 3:2 ratio"
	args={{
		posterImageUrl: "https://files.vidstack.io/sprite-fight/poster.webp"
	}}
></Story>

<Story
	name="Invalid image"
	args={{
		posterImageUrl: "https://localhost/poster.webp"
	}}
></Story>
