<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import UserMenu from "./UserMenu.svelte";

	const { Story } = defineMeta({
		title: "Components/UserMenu",
		component: UserMenu,
		argTypes: {
			user: {
				control: "object"
			}
		}
	});
</script>

<script lang="ts">
	import { AppUserState, getAppUserState, setAppUserState } from "$lib/states/appUserState.svelte";
	import { onMount } from "svelte";
	import type { StoryContext } from "@storybook/svelte";
	import { fn, userEvent, within, screen, expect } from "@storybook/test";
	import { sleep } from "$lib/utils/sleep";

	setAppUserState();
	const appUserState: AppUserState = getAppUserState();

	onMount(() => {
		// @ts-ignore
		switch (__STORYBOOK_PREVIEW__.currentRender.story.name) {
			case "WithUser":
			case "WithUserAndOpenMenu":
			case "WithDelayedUserRemoval":
			case "TestDropdownMenu":
				appUserState.user = {
					id: 1,
					username: "Username"
				};
				break;
		}
	});
</script>

<Story name="Primary"></Story>

<Story name="WithUser"></Story>

<Story
	name="WithUserAndOpenMenu"
	play={async (storyContext: StoryContext) => {
		const { canvasElement } = storyContext;
		const canvas = within(canvasElement);

		await userEvent.click(canvas.getByTestId("btn-user-menu"));
	}}
></Story>

<Story
	name="WithDelayedUserAddition"
	play={async () => {
		await sleep(1000);
		appUserState.user = {
			id: 1,
			username: "Username"
		};
	}}
></Story>

<Story
	name="WithDelayedUserRemoval"
	play={async () => {
		await sleep(1000);
		appUserState.user = undefined;
	}}
></Story>

<Story
	name="TestDropdownMenu"
	play={async (storyContext: StoryContext) => {
		const { canvasElement } = storyContext;
		const canvas = within(canvasElement);
		const rerollHandlerStub = fn();
		const changeHandlerStub = fn();
		const recoverHandlerStub = fn();

		appUserState.rerollUsernameSignal.on(rerollHandlerStub);
		appUserState.changeUsernameSignal.on(changeHandlerStub);
		appUserState.recoverUsernameSignal.on(recoverHandlerStub);

		await userEvent.click(canvas.getByTestId("btn-user-menu"));
		await userEvent.click(screen.getByRole("button", { name: "Reroll name" }));
		expect(rerollHandlerStub).toHaveBeenCalledOnce();

		await userEvent.click(canvas.getByTestId("btn-user-menu"));
		await userEvent.click(screen.getByRole("button", { name: "Change name" }));
		expect(changeHandlerStub).toHaveBeenCalledOnce();

		await userEvent.click(canvas.getByTestId("btn-user-menu"));
		await userEvent.click(screen.getByRole("button", { name: "Recover name" }));
		expect(recoverHandlerStub).toHaveBeenCalledOnce();
	}}
></Story>
