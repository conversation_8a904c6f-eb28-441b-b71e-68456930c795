<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import type { MutedBannerProps } from "$lib/models/props/mutedBannerProps";
	import { fly } from "svelte/transition";
	import { quadOut } from "svelte/easing";

	let { onClickUnmute }: MutedBannerProps = $props();
</script>

<div
	class="absolute inset-0 flex flex-col pointer-events-none z-0"
	transition:fly={{ y: -50, duration: 250, easing: quadOut }}
>
	<div class="flex">
		<div class="flex-1"></div>
		<div
			class="flex items-center bg-player-neutral-950/90 backdrop-blur rounded-xl px-6 py-4 mx-6 my-4 pointer-events-auto shadow-muted-banner"
		>
			<span>Playback started muted due to browser restrictions</span>
			<Button
				class="ml-8 shadows-button-bright-level0"
				variant="default"
				size="sm"
				onclick={() => onClickUnmute?.()}><span class="pb-[1px]">Unmute</span></Button
			>
		</div>
		<div class="flex-1"></div>
	</div>
	<div class="flex-1"></div>
</div>

<style>
	.shadow-muted-banner {
		box-shadow: inset 0px 1px 0.25px rgba(255, 255, 255, 0.18);
	}
</style>
