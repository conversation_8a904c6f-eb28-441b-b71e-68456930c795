<script lang="ts">
	import { type InfoColumnProps } from "$lib/models/props/infoColumnProps";
	import * as Tooltip from "$lib/components/ui/tooltip";
	import Separator from "../../separator/separator.svelte";

	let { items, separatorClass }: InfoColumnProps = $props();
</script>

{#each items as item, i}
	{#if item.contentItems && item.contentItems.length > 0}
		{#if i > 0}
			<Separator class="bg-background shadows-separator-level1 my-3 {separatorClass ?? ''}"
			></Separator>
		{/if}
		<p class="font-subheader text-md text-foreground-accent uppercase">{item.title}</p>

		<div class="flex flex-wrap gap-2 mt-1 mb-2">
			{#each item.contentItems as contentItem, i}
				{#if contentItem.imageUrl}
					<Tooltip.Root>
						<Tooltip.Trigger>
							<div class="w-auto h-[16px]">
								<img
									class="w-full h-full"
									src={contentItem.imageUrl}
									alt={contentItem.value}
									loading="lazy"
								/>
							</div>
						</Tooltip.Trigger>
						<Tooltip.Content>
							<span>{contentItem.value}</span>
						</Tooltip.Content>
					</Tooltip.Root>
				{:else}
					{@const isFollowedByTextItem =
						item.contentItems[i + 1] && !item.contentItems[i + 1].imageUrl}
					<p class="text-sm">{contentItem.value}{isFollowedByTextItem ? ", " : ""}</p>
				{/if}
			{/each}
		</div>
	{/if}
{/each}
