<script module>
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import RecoverPage from "./+page.svelte";
	import RootLayout from "../+layout.svelte";

	const { Story } = defineMeta({
		title: "Pages/Recover",
		component: RecoverPage,
		// @ts-ignore
		decorators: [() => RootLayout],
		parameters: { layout: "fullscreen" }
	});
</script>

<Story
	name="Primary"
	args={{
		data: {
			user: {
				id: 1,
				username: "User1",
				token: "testToken",
				email: "<EMAIL>",
				emailConfirmed: true
			}
		}
	}}
></Story>

<Story
	name="WithAPIError"
	args={{
		data: {
			error: "Failed to verify confirmation token"
		}
	}}
></Story>
