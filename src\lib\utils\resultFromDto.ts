import type { ResultDto, ValueResultDto } from '$lib/models/dto/socket/resultDto';
import { ResultErrorDtoType, type ResultErrorDto } from '$lib/models/dto/socket/resultErrorDto';
import { ExceptionalError } from '$lib/models/result/exceptionalError';
import { InvalidError } from '$lib/models/result/invalidError';
import { NotFoundError } from '$lib/models/result/notFoundError';
import { fail, ok, type Result, type ValueResult } from '$lib/models/result/result';
import { ResultError } from '$lib/models/result/resultError';
import { UnauthorizedError } from '$lib/models/result/unauthorizedError';

export function resultFromDto(resultDto: ResultDto): Result {
	if (resultDto.isSuccess) {
		return ok();
	}

	return failedResultFromErrorDto(resultDto.error);
}

export function valueResultFromDto<TValue>(
	valueResultDto: ValueResultDto<TValue>
): ValueResult<TValue> {
	if (valueResultDto.isSuccess) {
		return ok<TValue>(valueResultDto.value);
	}

	return failedResultFromErrorDto<TValue>(valueResultDto.error);
}

function failedResultFromErrorDto(resultErrorDto: ResultErrorDto): Result;
function failedResultFromErrorDto<TValue>(resultErrorDto: ResultErrorDto): ValueResult<TValue>;
function failedResultFromErrorDto(resultErrorDto: ResultErrorDto): Result {
	const message = resultErrorDto.message;
	let error: ResultError;
	switch (resultErrorDto.type) {
		case ResultErrorDtoType.Exceptional:
			error = new ExceptionalError(message);
			break;
		case ResultErrorDtoType.Invalid:
			error = new InvalidError(message);
			break;
		case ResultErrorDtoType.NotFound:
			error = new NotFoundError(message);
			break;
		case ResultErrorDtoType.Unauthorized:
			error = new UnauthorizedError(message);
			break;
		case ResultErrorDtoType.Default:
		default:
			error = new ResultError(message);
			break;
	}

	return fail(error);
}
