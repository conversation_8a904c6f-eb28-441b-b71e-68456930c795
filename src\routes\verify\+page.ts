import { ApiService } from "$lib/services/apiService";
import { error } from "@sveltejs/kit";
import type { PageLoadEvent } from "./$types";
import { ChangeUsernameStatus } from "$lib/models/dto/responses/changeUsernameResponse";

export async function load(event: PageLoadEvent) {
	const fetch = event.fetch;
	const id = Number(event.url.searchParams.get("id"));
	const token = event.url.searchParams.get("token");
	const username = event.url.searchParams.get("username");
	const apiService = new ApiService(fetch);

	if (isNaN(id)) {
		throw error(422, "Provided id is invalid");
	}

	if (!token) {
		throw error(422, "Confirmation token not provided");
	}

	const confirmationResponse = await apiService.verifyConfirmationToken(id, token);
	if (!confirmationResponse.ok || confirmationResponse.body === undefined) {
		return {
			error: confirmationResponse.error?.message ?? "Failed to verify confirmation token"
		};
	}

	let user = confirmationResponse.body;
	let isUsernameSet = false;
	let usernameError = "";
	if (username) {
		const changeUsernameResponse = await apiService.changeUsername(id, username, "", user.token);
		if (!changeUsernameResponse.ok || changeUsernameResponse.body === undefined) {
			usernameError = changeUsernameResponse.error?.message ?? "Failed to change username";
		} else {
			switch (changeUsernameResponse.body.status) {
				case ChangeUsernameStatus.Unchanged:
					if (changeUsernameResponse.body.user) {
						user = changeUsernameResponse.body.user;
					}
					isUsernameSet = false;
					break;
				case ChangeUsernameStatus.Changed:
					if (changeUsernameResponse.body.user) {
						user = changeUsernameResponse.body.user;
					}
					isUsernameSet = true;
					break;
				case ChangeUsernameStatus.UsernameAlreadyTaken:
					usernameError = "Username already taken";
					break;
				default:
					usernameError = "Failed to change username";
			}
		}
	}

	return {
		user,
		isUsernameSet,
		usernameError
	};
}
