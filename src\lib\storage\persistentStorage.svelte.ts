import { browser } from "$app/environment";
import { LocalStorageProvider } from "./localStorageProvider";
import type { StorageProvider } from "./storageProvider";

export class PersistentStorage<TValue> {
	private _key: string = "";
	value: TValue | undefined = $state();

	get key() {
		return this._key;
	}

	constructor(
		key: string,
		defaultValue?: TValue,
		storageProvider: StorageProvider = new LocalStorageProvider()
	) {
		this._key = key;
		this.value = defaultValue;

		if (browser) {
			const storedValue = storageProvider.getItem(key);
			if (storedValue !== undefined) {
				this.value = this.deserialize(storedValue);
			}
		}

		$effect(() => {
			if (this.value === undefined) {
				storageProvider.removeItem(key);
			} else {
				storageProvider.setItem(key, this.serialize(this.value));
			}
		});
	}

	private serialize(value: TValue) {
		return JSON.stringify(value);
	}

	private deserialize(value: string) {
		return JSON.parse(value);
	}
}
