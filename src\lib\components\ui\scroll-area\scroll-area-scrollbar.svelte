<script lang="ts">
	import { ScrollArea as ScrollAreaPrimitive, type WithoutChild } from "bits-ui";
	import { cn } from "$lib/utils.js";

	let {
		ref = $bindable(null),
		class: className,
		orientation = "vertical",
		children,
		...restProps
	}: WithoutChild<ScrollAreaPrimitive.ScrollbarProps> = $props();
</script>

<ScrollAreaPrimitive.Scrollbar
	bind:ref
	{orientation}
	class={cn(
		"flex touch-none select-none transition-colors hover:bg-player-neutral-900/75",
		orientation === "vertical" &&
			"h-full w-2.5 border-l-4 border-l-transparent pl-px py-px pr-0.5 hover:border-l",
		orientation === "horizontal" &&
			"h-2.5 w-full border-t-4 border-t-transparent pt-px px-px pb-0.5 hover:border-t",
		className
	)}
	{...restProps}
>
	{@render children?.()}
	<ScrollAreaPrimitive.Thumb
		class={cn(
			"bg-player-neutral-400/50 relative rounded-full active:bg-player-neutral-400/40",
			orientation === "vertical" && "flex-1"
		)}
	/>
</ScrollAreaPrimitive.Scrollbar>
